# API Specifications

Centralized OpenAPI specifications for all DEUSS offchain microservices. This module consolidates and manages OpenAPI YAML files from different services, providing a unified location for API documentation and code generation.

## What it contains

This module organizes OpenAPI specifications in the following structure:

```
src/main/resources/
├── common/
│   └── common_constructs_openapi.yaml    # Shared components (errors, parameters, schemas)
├── company-service/
│   ├── public/
│   │   └── company-service.yaml          # Public API endpoints
│   ├── internal/
│   │   └── company-service-internal.yaml # Internal API endpoints
│   └── company-service-common.yaml       # Service-specific shared components
├── file-service/
│   └── internal/
│       └── file-service.yaml             # File service API endpoints
└── user-service/                         # Other services
```

### Components

- **Common constructs** (`common/`): Reusable OpenAPI components shared across all services
  - Standard HTTP error responses (400, 401, 403, 404, 409)
  - Common parameters (pagination: `Page`, `PageSize`)
  - Shared schemas (e.g., `HttpError` following RFC 9457)

- **Service specifications**: Each service can have:
  - **Public APIs**: External-facing endpoints for client applications. Multiple files in directory are supported and encouraged.
  - **Internal APIs**: Service-to-service communication endpoints. Multiple files in directory are supported and encouraged.
  - **Service-common**: Shared components specific to that service

## How to use it

### Building OpenAPI specifications

Generate merged OpenAPI YAML files for all services:

```bash
# in root of the project
./gradlew api-specifications:build
```

This will create merged YAML files in:
- `build/company-service/public.yaml`
- `build/company-service/internal.yaml`
- `build/company-service/full.yaml`
etc.

## Adding new services

To add OpenAPI specifications for a new service:

1. **Create service directory structure**:
   ```bash
   mkdir -p src/main/resources/<your-service-name>/public
   mkdir -p src/main/resources/<your-service-name>/internal
   ```

2. **Add OpenAPI specification files**:
   - Place public API specs in `your-service-name/public/`
   - Place internal API specs in `your-service-name/internal/`
   - Optionally create `your-service-name-common.yaml` for service-specific shared components

3. **Register in build.gradle.kts**:
   ```kotlin
   registerServiceOpenApiGeneration("your-service-name")
   ```

4. **Reference common constructs**:
   Use relative paths to reference shared components:
   ```yaml
   responses:
     '400':
       $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
   ```

### Best practices

- **Use common constructs**: Reference shared error responses and parameters from `common/common_constructs_openapi.yaml`
- **Separate public/internal**: Keep external-facing APIs separate from internal service-to-service APIs
- **Service-specific commons**: Create `service-name-common.yaml` for components shared within a service but not globally
- **Consistent naming**: Follow the pattern `service-name.yaml` for main specifications
- **Proper references**: Use relative paths when referencing other specification files

## Build process

The module uses the OpenAPI Generator Gradle plugin to:

1. **Merge specifications**: Combines multiple YAML files from each service directory
2. **Validate**: Ensures OpenAPI specifications are valid
3. **Generate output**: Creates consolidated YAML files in `build/open-api/`

### Configuration

The build process is configured in `build.gradle.kts`:
- **Input**: Service directories in `src/main/resources/`
- **Output**: Merged YAML files in `build/openapi/`

### Generated files

For each service with public/internal directories, the following files are generated:
- `build/{service-name}/public.yaml`: Merged public API specification
- `build/{service-name}/internal.yaml`: Merged internal API specification
- `build/{service-name}/full.yaml`: Merged all API specifications belonging to service.

## Integration with services

Individual microservices can reference these centralized specifications for:
- **Server code generation**: Generate controllers, models, and validation
- **Client code generation**: Generate HTTP clients for service-to-service communication
- **Documentation**: Serve API documentation using generated specifications
