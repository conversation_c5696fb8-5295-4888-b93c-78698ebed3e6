openapi: 3.0.0
paths: { }
info:
  title: DEUSS Common Error API
  version: 1.12.1
  description: Common API constructs for DEUSS microservices
components:
  responses:
    ErrorResponse:
      description: An error occurred
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/HttpError"
    UnauthorizedErrorResponse:
      description: Unauthorized access, user must be logged in
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/HttpError"
    NotFoundErrorResponse:
      description: Resource not found by id
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/HttpError"
    BadRequestErrorResponse:
      description: Invalid input data
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/HttpError"
    ForbiddenErrorResponse:
      description: User does not have enough permissions to perform this action
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/HttpError"
    ConflictErrorResponse:
      description: Resource cannot be created
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/HttpError"

  parameters:
    PageSize:
      in: query
      name: pageSize
      description: Sets the page size/number of returned elements
      required: true
      example: 25
      schema:
        type: integer
        format: int32
        minimum: 1
        maximum: 100
        default: 25
    Page:
      name: page
      in: query
      description: Sets which page should be returned
      required: true
      example: 0
      schema:
        type: integer
        format: int32
        minimum: 0
        default: 0



  schemas:
    HttpError:
      type: object
      description: |
        A Problem Details object (RFC 9457).
        Additional properties specific to the problem type may be present.
      properties:
        type:
          type: string
          format: uri
          description: An absolute URI that identifies the problem type
          default: about:blank
          example: cz:deuss:BaseProblem
        title:
          type: string
          description: A short summary of the problem type. Written in English and readable for engineers (usually not suited for non technical stakeholders and not localized).
          example: Service Unavailable
        status:
          type: integer
          format: int32
          description: The HTTP status code generated by the origin server for this occurrence of the problem.
          minimum: 400
          maximum: 600
          exclusiveMaximum: true
          example: 418
        detail:
          type: string
          description: A human-readable explanation specific to this occurrence of the problem
          example: I am a teapot
        instance:
          type: string
          format: uri
          description: An absolute URI that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
          example: /company/12345
        timestamp:
          type: string
          format: date-time
          description: Timestamp in ISO 8601 format (e.g., YYYY-MM-DDTHH:MM:SS.sssZ)
          example: 2025-02-11T14:30:00.123Z
        messageDetails:
          type: array
          items:
            $ref: "#/components/schemas/MessageDetail"
      required:
        - type
        - detail
        - messageDetails
        - messageId
        - status
        - timestamp
        - title
    MessageDetail:
      type: object
      description: Detail containing further data for frontend error messages like names of missing/invalid params
      example: "emailAddress: <EMAIL>"
      properties:
        name:
          type: string
          description: Name of parameter
          example: "invalidEmails"
        values:
          type: array
          items:
            type: string
          description: Values specified for parameter
          example: [ "<EMAIL>", "<EMAIL>" ]

    PagedResultMetadata:
      type: object
      properties:
        count:
          type: integer
          format: int64
          description: Total count of results which can be returned for API call with current settings
          example: 100
        page:
          type: integer
          format: int32
          description: Page for which results are returned
          example: 0
      required:
        - count
        - page

    OffChainId:
      properties:
        id:
          type: string
          format: uuid
          description: Unique off chain identifier
          example: a715268c-6b20-4ccc-9779-c6f5e68a577f
      required:
        - id
    WalletAddress:
      type: string
      description: "The hexadecimal string representing an Ethereum wallet address."
      pattern: '^0x[a-fA-F0-9]{40}$'
      example: '******************************************'
    AuditTimestamp:
      properties:
        created:
          type: string
          format: date-time
          description: Date when the company was added to system. Datetime is sent in ISO 8166 format in UTC.
          example: "2023-01-15T10:00:00.000Z"
        last_edit:
          type: string
          format: date-time
          description: Date when the company was last edited. Datetime is sent in ISO 8166 format in UTC.
          example: "2023-02-10T10:00:00.000Z"
      required:
        - created
        - last_edit
    PreciseNumber:
      description: "A number represented as a string with exactly two decimal places."
      type: string
      format: decimal
      nullable: false
      x-field-extra-annotation:
        - "@Digits(message = \"Value must have at most {integer} digits and at most {fraction} digits after decimal point\", integer = 24, fraction = 2)"
        - "@DecimalMin(message = \"Provided value must be at least {value}\" , value = \"0.01\", inclusive = true)"
    CountryCode:
      description: Countries in EU
      example: CZ
      type: string
      enum:
        - AL
        - AD
        - AT
        - AZ
        - BY
        - BE
        - BA
        - BG
        - HR
        - CY
        - CZ
        - DK
        - EE
        - FI
        - FR
        - GE
        - DE
        - GR
        - HU
        - IS
        - IE
        - IT
        - KZ
        - XK
        - LV
        - LI
        - LT
        - LU
        - MK
        - MT
        - MD
        - MC
        - ME
        - NL
        - "NO" # has to be quoted, otherwise generates "FALSE"
        - PL
        - PT
        - RO
        - RU
        - SM
        - RS
        - SK
        - SI
        - ES
        - SE
        - CH
        - TR
        - UA
        - GB
        - VA
    Currency:
      description: Name of european currency used
      type: string
      enum:
        - EUR
        - CZK
        - BGN
        - DKK
        - HUF
        - PLN
        - RON
        - SEK
        - CHF
    OrderDirection:
      example: "ASC"
      description: Sort order which should be applied for the field.
      type: string
      enum:
        - ASC
        - DESC