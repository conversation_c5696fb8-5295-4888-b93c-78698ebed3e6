openapi: 3.0.0
info:
  title: Bond issuance Service API
  version: 1.4.1
tags:
  - name: Issuance Draft
    description: Endpoints related to bond issuance drafts
  - name: Issuance Draft Document
    description: Endpoints related to bond issuance documents which can be uploaded to issuance drafts
  - name: Issuance Draft Marketing Asset
    description: Endpoints related to bond issuance marketing assets which can be uploaded to issuance drafts
  - name: Published Issuance Files
    description: Endpoints related to published bond issuance including documents and marketing assets
  - name: Validation
    description: Endpoints related to bond issuance validation and validation requests
paths:
  /issuances/drafts/{issuance_id}:
    parameters:
      - $ref: "#/components/parameters/IssuanceId"
    get:
      operationId: getIssuanceDraft
      summary: Retrieve a specific bond issuance draft
      tags:
        - Issuance Draft
      responses:
        '200':
          $ref: "#/components/responses/IssuanceDraftResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    patch:
      summary: Update an existing bond issuance draft
      operationId: updateIssuanceDraft
      tags:
        - Issuance Draft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IssuanceDraftUpdate'
      responses:
        '200':
          $ref: "#/components/responses/IssuanceDraftResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

    delete:
      operationId: deleteIssuanceDraft
      summary: Delete an issuance draft
      tags:
        - Issuance Draft
      responses:
        '204':
          description: Issuance draft deleted successfully
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /issuances/drafts:
    get:
      tags:
        - Issuance Draft
      operationId: get_issuance_drafts
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      summary: Returns paged list of issuance drafts for administrator to list them all by his needs.
      responses:
        '200':
          $ref: "#/components/responses/PagedIssuanceDraftsResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

    post:
      operationId: createIssuanceDraft
      summary: Create a new bond issuance draft
      tags:
        - Issuance Draft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IssuanceDraftCreate"
      responses:
        '201':
          $ref: "#/components/responses/IssuanceDraftResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
  /issuances/drafts/my:
    get:
      tags:
        - Issuance Draft
      operationId: get_my_issuance_drafts
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
        - $ref: "#/components/parameters/CompanyIdQueryParam"
      summary: Returns paged list of issuance drafts for company administrator/editor/viewer. Can be filtered by company.
      responses:
        '200':
          $ref: "#/components/responses/PagedIssuanceDraftsResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
  /issuances/drafts/{issuance_id}/documents:
    parameters:
      - $ref: "#/components/parameters/IssuanceId"
    post:
      operationId: createDocumentForIssuanceDraft
      summary: Upload a document related to an issuance draft
      tags:
        - Issuance Draft Document
        - Issuance Draft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BondIssuanceDraftDocumentCreateRequest"
      responses:
        '201':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IssuanceDraftDocumentDetailResponse"

        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
    get:
      operationId: getDocumentsForIssuanceDraft
      summary: Retrieves metadata of documents stored in bond issuance service
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      tags:
        - Issuance Draft Document
        - Issuance Draft
      responses:
        '200':
          description: Paged list of documents
          content:
            application/json:
              schema:
                title: PagedDraftDocuments
                type: object
                allOf:
                  - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"
                  - properties:
                      documents:
                        type: array
                        items:
                          $ref: "#/components/schemas/IssuanceDraftDocumentSummaryResponse"
                    required:
                      - documents
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
  /documents/{document_id}:
    parameters:
      - $ref: "#/components/parameters/DocumentId"
    delete:
      operationId: delete_document_by_id
      summary: Delete a file related to issuance draft
      tags:
        - Issuance Draft Document
        - Issuance Draft
      responses:
        '204':
          description: Issuance draft file deleted successfully
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    get:
      operationId: get_document_detail_by_id
      summary: Returns document detail by id for issuance
      tags:
        - Issuance Draft Document
        - Issuance Draft
      responses:
        '200':
          description: Issuance draft file detail fetched by id
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IssuanceDraftDocumentDetailResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /issuances/drafts/{issuance_id}/marketing-assets:
    parameters:
      - $ref: "#/components/parameters/IssuanceId"
    post:
      operationId: createMarketingAssetForIssuanceDraft
      summary: Upload a marketing asset such as video or image related to an issuance draft
      tags:
        - Issuance Draft Marketing Asset
        - Issuance Draft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BondIssuanceDraftMarketingAssetCreateRequest"
      responses:
        '201':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IssuanceDraftMarketingAssetDetailResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
    get:
      operationId: getMarketingAssetsForIssuanceDraft
      summary: Retrieves metadata of documents stored in bond issuance service
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      tags:
        - Issuance Draft Marketing Asset
        - Issuance Draft
      responses:
        '200':
          description: Paged list of marketing assets
          content:
            application/json:
              schema:
                title: PagedDraftMarketingAssets
                type: object
                allOf:
                  - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"
                  - properties:
                      marketing_assets:
                        type: array
                        items:
                          $ref: "#/components/schemas/IssuanceDraftMarketingAssetSummaryResponse"
                    required:
                      - marketing_assets
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /marketing-assets/{marketing_asset_id}:
    parameters:
      - $ref: "#/components/parameters/MarketingAssetId"
    delete:
      operationId: deleteMarketingAssetById
      summary: Delete a marketing asset related to issuance draft
      tags:
        - Issuance Draft Marketing Asset
        - Issuance Draft
      responses:
        '204':
          description: Issuance marketing asset deleted successfully
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    get:
      operationId: get_marketing_asset_detail
      summary: Get a marketing asset detail by id
      tags:
        - Issuance Draft Marketing Asset
        - Issuance Draft
      responses:
        '200':
          description: Get issuance marketing asset by id
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IssuanceDraftMarketingAssetDetailResponse"

        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /issuances/drafts/{issuance_id}/validation:
    parameters:
      - $ref: "#/components/parameters/IssuanceId"
    post:
      summary: Creates new validation request for issuance draft
      operationId: createValidationRequestForIssuanceDraft
      tags:
        - Validation
        - Issuance Draft
      responses:
        '200':
          $ref: "#/components/responses/ValidationRequestResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
    patch:
      summary: Validation result provided by validation entity for issuance draft
      operationId: finishValidationRequestForIssuanceDraft
      tags:
        - Validation
        - Issuance Draft
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ValidationRequestResult"
      responses:
        '200':
          $ref: "#/components/responses/ValidationRequestResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
    get:
      operationId: getValidationRequestsForIssuanceDraft
      tags:
        - Validation
        - Issuance Draft
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "#/components/parameters/ValidationRequestsOrder"
      summary: Returns validation requests requested for this issuance by specified query
      description: Returns validation requests requested for this issuance by specified query
      responses:
        '200':
          description: List of all validation requests for this issuance
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ValidationRequestDetail"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

components:
  parameters:
    IssuanceId:
      name: issuance_id
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: be41bbdf-0dc7-4dcc-ab69-ffabe088b59c
    FileId:
      name: file_id
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: 73a14680-b9ff-46fa-8e20-1262f2f9ff00

    MarketingAssetId:
      name: marketing_asset_id
      in: path
      required: true
      description: Unique off chain identifier of marketing asset
      schema:
        type: string
        format: uuid
        example: 73a14680-b9ff-46fa-8e20-1262f2f9ff00

    DocumentId:
      name: document_id
      in: path
      required: true
      description: Unique off chain identifier of document
      schema:
        type: string
        format: uuid
        example: 73a14680-b9ff-46fa-8e20-1262f2f9ff00

    CompanyIdQueryParam:
      name: company_id
      in: query
      required: false
      schema:
        type: string
        format: uuid
        example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      description: Optional filter to retrieve companies associated with a specific company ID.

    ValidationRequestsOrder:
      name: sort
      in: query
      required: false
      explode: true
      style: deepObject
      schema:
        $ref: "#/components/schemas/ValidationRequestSortOrders"

  responses:
    IssuanceDraftResponse:
      description: Issuance detail returned by endpoint
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/IssuanceDraftResponse"
    PagedIssuanceDraftsResponse:
      description: Paged list of issuance drafts
      content:
        application/json:
          schema:
            title: PagedIssuanceDraftsResponse
            type: object
            allOf:
              - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"
              - properties:
                  issuance_drafts:
                    type: array
                    items:
                      $ref: "#/components/schemas/IssuanceDraftResponse"
                required:
                  - issuance_drafts
    ValidationRequestResponse:
      description: Validation request response with actual data about validation
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ValidationRequestDetail"
  requestBodies:
    File:
      description: File which is uploaded with issuance
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              file:
                type: string
                format: binary
  schemas:
    IssuanceDraftResponse:
      type: object
      allOf:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/OffChainId"
        - $ref: "#/components/schemas/IssuanceDraftUpdate"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/AuditTimestamp"
        - properties:
            ISIN:
              type: string
              example: DE000BAY0017
            volume:
              $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PreciseNumber"
            scoring:
              $ref: "#/components/schemas/Scoring"
            state:
              $ref: "#/components/schemas/IssuanceState"
            country:
              $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"
            company_id:
              type: string
              format: uuid
              description: Unique off chain identifier of company
              example: a715268c-6b20-4ccc-9779-c6f5e68a577f
      required:
        - state
        - country
        - company_id-


    IssuanceDraftCreate:
      type: object
      properties:
        name:
          type: string
          x-field-extra-annotation: "@NotBlank"
          maxLength: 255
        country:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"
        company_id:
          format: uuid
          type: string
      required:
        - name
        - company_id
        - country

    IssuanceDraftUpdate:
      type: object
      properties:
        maturity:
          type: string
          format: date
          x-field-extra-annotation: "@Future(message = \"Field maturity must be in future.\")"
        description:
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          type: string
          maxLength: 10_000
        currency:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/Currency"
        bond_nominal_value:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PreciseNumber"
        bond_count:
          type: integer
          minimum: 0
        interest:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PreciseNumber"
        issue_date:
          type: string
          format: date
          x-field-extra-annotation: "@Future(message = \"Field issue_date must be in future.\")"
        interest_payment_type:
          $ref: "#/components/schemas/InterestPaymentRate"
        name:
          type: string
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          maxLength: 255
        ISIN:
          type: string
          example: DE000BAY0017
          x-field-extra-annotation: "@cz.deuss.bondissuanceservice.validation.annotation.ValidIsinOrNull"
        industry_code:
          $ref: "#/components/schemas/IndustryCode"
        scoring:
          $ref: "#/components/schemas/Scoring"

    ValidationRequestDetail:
      description: Validation request detail which serves as body for response with state of validation of issuance
      type: object
      allOf:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/OffChainId"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/AuditTimestamp"
        - type: object
          properties:
            status:
              $ref: "#/components/schemas/ValidationStatus"
            result:
              $ref: "#/components/schemas/ValidationResult"
            reason:
              type: string
              x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
      required:
        - status

    ValidationRequestResult:
      description: Validation result provided by validation entity
      type: object
      properties:
        result:
          $ref: "#/components/schemas/ValidationResult"
        reason:
          type: string
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
      required:
        - result
    IssuanceState:
      type: string
      enum:
        - DRAFT
        - WAITING_FOR_REVIEW
        - IN_REVIEW
        - APPROVED
        - CHANGES_REQUESTED
        - PUBLISHED
    ValidationResult:
      type: string
      enum:
        - APPROVED
        - CHANGES_REQUESTED
    ValidationStatus:
      type: string
      enum:
        - WAITING_FOR_VALIDATION
        - IN_VALIDATION
        - VALIDATED
    InterestPaymentRate:
      type: string
      description: Type describing how often is interest payment paid
      enum:
        - SEMI_ANNUAL
        - ANNUAL
        - QUARTERLY
        - MONTHLY
        - ZERO_COUPON
    ValidationRequestSortOrders:
      title: SortOrders
      type: array
      items:
        type: object
        title: ValidationRequestOrderDirection
        properties:
          order_by:
            title: ValidationRequestAttributeName
            type: string
            enum:
              - CREATED
              - VALIDATED
              - STATUS
          order:
            $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/OrderDirection"
        required:
          - order
          - order_by
    BaseFileMetadata:
      type: object
      allOf:
        - $ref: "#/components/schemas/BaseFileSummaryMetadata"
        - properties:
            dms_path:
              type: string
              format: uri
              description: Dms url from which can file be downloaded
              example: https://static.wikia.nocookie.net/brainrotnew/images/e/e3/Bombini_Gusini.jpg/revision/latest/thumbnail/width/360/height/450?cb=20250416185048
          required:
            - dms_path

    IssuanceDraftMarketingAssetDetailResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/BaseFileMetadata"
        - properties:
            file_type:
              $ref: "#/components/schemas/MarketingAssetFileType"
            issuance_draft_id:
              type: string
              format: uuid
              example: 5b4454b6-d5a0-420c-9927-e3bdd8096bb9
              description: Bond issuance draft identifier on offchain
      required:
        - file_type
        - issuance_draft_id

    IssuanceDraftDocumentDetailResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/BaseFileMetadata"
        - properties:
            file_type:
              $ref: "#/components/schemas/DocumentFileType"
            issuance_draft_id:
              type: string
              format: uuid
              example: 5b4454b6-d5a0-420c-9927-e3bdd8096bb9
              description: Bond issuance draft identifier on offchain
          required:
            - file_type
            - issuance_draft_id

    BaseFileSummaryMetadata:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Id of a file
          example: 3f3a3cd0-9697-45d2-b61a-822eddd2f970
        created_at:
          type: string
          format: date-time
          description: DateTime in which was file uploaded
          example: "2023-01-15T10:00:00.000Z"
        creator_id:
          type: string
          format: uuid
          description: Id of user which created/uploaded this file
          example: 1cbd890d-1529-4857-aafa-b6093e698ce9
        name:
          type: string
          maxLength: 255
          example: why_are_we_still_here.jpg
      required:
        - id
        - created_at
        - creator_id
        - name

    IssuanceDraftDocumentSummaryResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/BaseFileSummaryMetadata"
        - properties:
            file_type:
              $ref: "#/components/schemas/DocumentFileType"
            issuance_draft_id:
              type: string
              format: uuid
              example: 5b4454b6-d5a0-420c-9927-e3bdd8096bb9
              description: Bond issuance draft identifier on offchain
          required:
            - file_type
            - issuance_draft_id
    IssuanceDraftMarketingAssetSummaryResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/BaseFileSummaryMetadata"
        - properties:
            dms_path:
              type: string
              format: uri
              description: Dms url from which can file be downloaded
              example: https://static.wikia.nocookie.net/brainrotnew/images/e/e3/Bombini_Gusini.jpg/revision/latest/thumbnail/width/360/height/450?cb=20250416185048
            type:
              $ref: "#/components/schemas/MarketingAssetFileType"
      required:
        - type


    IssuanceDocumentResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/BaseFileMetadata"
        - properties:
            file_type:
              $ref: "#/components/schemas/DocumentFileType"
            issuance_id:
              type: string
              format: uuid
              example: 5b4454b6-d5a0-420c-9927-e3bdd8096bb9
              description: Bond issuance identifier on offchain
          required:
            - file_type
            - issuance_id
      required:
        - type
    Scoring:
      type: string
      enum: [ AAA, AA, A, BBB, BB, B, CCC, CC, C, D ]
      example: "BB"
      description: Scoring of bond

    IndustryCode:
      type: string
      description: "NACE industry classification codes"
      enum:
        - A  # Agriculture, Forestry and Fishing
        - B  # Mining and Quarrying
        - C  # Manufacturing
        - D  # Electricity, Gas, Steam and Air Conditioning Supply
        - E  # Water Supply; Sewerage, Waste Management and Remediation Activities
        - F  # Construction
        - G  # Wholesale and Retail Trade; Repair of Motor Vehicles and Motorcycles
        - H  # Transportation and Storage
        - I  # Accommodation and Food Service Activities
        - J  # Information and Communication
        - K  # Financial and Insurance Activities
        - L  # Real Estate Activities
        - M  # Professional, Scientific and Technical Activities
        - N  # Administrative and Support Service Activities
        - O  # Public Administration and Defence; Compulsory Social Security
        - P  # Education
        - Q  # Human Health and Social Work Activities
        - R  # Arts, Entertainment and Recreation
        - S  # Other Service Activities
        - T  # Activities of Households as Employers
        - U  # Activities of Extraterritorial Organizations and Bodies
      example: "C"

    BondIssuanceDraftDocumentCreateRequest:
      description: Document creation request for bond issuance draft
      type: object
      properties:
        document_name:
          type: string
          maxLength: 255
          x-extra-annotation: "@NotBlank"
          description: Name of the document without extension
        file_type:
          $ref: "#/components/schemas/DocumentFileType"
      required:
        - document_name
        - file_type
      example:
        document_name: 'Q4 Report'
        file_type: 'PDF'

    BondIssuanceDraftMarketingAssetCreateRequest:
      description: Marketing asset creation request for bond issuance draft
      type: object
      properties:
        marketing_asset_name:
          type: string
          maxLength: 255
          x-extra-annotation: "@NotBlank"
          description: Name of the asset without extension
        file_type:
          $ref: "#/components/schemas/MarketingAssetFileType"
      required:
        - marketing_asset_name
        - file_type
      example:
        document_name: 'Q4 Report'
        file_type: 'PNG'

    DocumentFileType:
      type: string
      description: File type of sent document
      enum:
        - PDF
        - DOC
        - DOCX
        - XLS
        - XLSX
        - ODP
        - ODT
        - ODS
        - PPT
        - PPTX
        - RTF
    MarketingAssetFileType:
      type: string
      enum:
        - MP4
        - PNG
        - PDF
