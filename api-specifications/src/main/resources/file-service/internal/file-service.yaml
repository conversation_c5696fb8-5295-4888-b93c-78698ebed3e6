openapi: 3.0.0
info:
  title: File Service API
  version: 2.1.0
  description: API for managing files
paths:
  /file-groups/{service_name}/list:
    get:
      tags:
        - Files
      summary: Get the list of files of the backend service
      operationId: getFilesList
      parameters:
        - $ref: "#/components/parameters/serviceName"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/continuationToken"
      responses:
        "200":
          description: List of files returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetFilesListResponse"
        "401":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "403":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        "404":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
  /file-groups/{service_name}/files/{key}/presign-get:
    post:
      tags:
        - Files
      summary: Generate presigned GET URL. Allow to download file from predetermined identifier.
      operationId: generate_presigned_get_url
      parameters:
        - $ref: "#/components/parameters/serviceName"
        - $ref: "#/components/parameters/fileKey"
      requestBody:
        $ref: "#/components/requestBodies/GeneratePresignedGetFileUrlRequest"
      responses:
        "200":
          description: File URL returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneratePresignedGetFileUrlResponse"
        "401":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "404":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
  /file-groups/{service_name}/files/{key}/presign-put:
    post:
      tags:
        - Files
      summary: Generate presigned PUT URL. Allow to upload file to predetermined identifier in storage.
      operationId: generate_presigned_put_url
      parameters:
        - $ref: "#/components/parameters/serviceName"
        - $ref: "#/components/parameters/fileKey"
      requestBody:
        $ref: "#/components/requestBodies/GeneratePresignedPutFileUrlRequest"
      responses:
        "200":
          description: File URL returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GeneratePresignedPutFileUrlResponse"
        "401":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "400":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
    delete:
      tags:
        - Files
      summary: Delete file by key
      operationId: deleteFileByKey
      parameters:
        - $ref: "#/components/parameters/serviceName"
        - $ref: "#/components/parameters/fileKey"
      responses:
        "204":
          description: File deleted successfully
        "401":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "403":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        "404":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
  /file-groups/{service_name}/files/{key}/metadata:
    get:
      tags:
        - Files
      summary: Get metadata of the file
      operationId: getFileMetadata
      parameters:
        - $ref: "#/components/parameters/serviceName"
        - $ref: "#/components/parameters/fileKey"
      responses:
        "200":
          description: File metadata returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetFileMetadataResponse"
        "401":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "403":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        "404":
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
components:
  requestBodies:
    GeneratePresignedGetFileUrlRequest:
      description: Generate GET url for file object.
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GeneratePresignedGetFileUrlRequest"
    GeneratePresignedPutFileUrlRequest:
      description: Generate PUT url for file object.
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GeneratePresignedPutFileUrlRequest"
  parameters:
    serviceName:
      name: service_name
      in: path
      description: The name of the backend service
      required: true
      schema:
        type: string
    fileKey:
      name: key
      in: path
      description: The key of the file
      required: true
      schema:
        type: string
    limit:
      name: limit
      in: query
      description: Limit of the entries to return
      required: false
      schema:
        type: integer
        default: 100
    continuationToken:
      name: continuation_token
      in: query
      description: String token referring to the next page of data
      required: false
      schema:
        type: string
  schemas:
    FileMetadataEntry:
      type: object
      description: A single metadata entry for a file
      properties:
        key:
          type: string
          description: The key of the metadata
        value:
          type: string
          description: The value of the metadata
      required:
        - key
        - value

    FileMetadata:
      type: array
      description: The metadata of the file
      items:
        $ref: "#/components/schemas/FileMetadataEntry"
    GetFilesListResponse:
      type: object
      properties:
        files:
          type: array
          required: true
          title: Files
          items:
            type: object
            properties:
              key:
                type: string
                description: File key
                required: true
              size:
                type: integer
                format: int64
                description: File size
                required: true
              last_modified:
                type: string
                format: date-time
                description: Last modified date of the file
                required: true
        continuation_token:
          type: string
          description: String token referring to the next page of data
          required: false
          nullable: true
    GeneratePresignedGetFileUrlRequest:
      type: object
      properties:
        file_name:
          type: string
          description: Filename used as attachment for presigned url
        signature_duration:
          type: string
          format: duration
          example: PT2H15M
        scope:
          $ref: "#/components/schemas/PresignedUrlScope"
      required:
        - file_name
        - signature_duration
        - scope
    GeneratePresignedGetFileUrlResponse:
      type: object
      properties:
        presigned_url:
          type: string
          format: uri
          description: Duration restricted presigned URL, which allow to download file till expiration is reached. Download can be invoked by any client without further authorization or authentication.
      required:
        - presigned_url
    GeneratePresignedPutFileUrlRequest:
      type: object
      properties:
        signature_duration:
          type: string
          format: duration
          example: PT2H15M
        scope:
          $ref: "#/components/schemas/PresignedUrlScope"
        metadata:
          $ref: "#/components/schemas/FileMetadata"
        content_length:
          type: integer
          format: int64
        checksum_sha256:
          description: "SHA256 checksum in base64 format."
          type: string
        content_type:
          type: string
      required:
        - signature_duration
        - scope
    GeneratePresignedPutFileUrlResponse:
      type: object
      properties:
        presigned_url:
          type: string
          format: uri
          description: Duration restricted presigned URL, which allow to upload file till expiration is reached. Upload can be invoked by any client without further authorization or authentication.
      required:
        - presigned_url
    GetFileMetadataResponse:
      type: object
      properties:
        key:
          type: string
          description: File key
        content-length:
          type: integer
          format: int64
          description: Length of the file content
        content-type:
          type: string
          description: Content type of the file
        metadata:
          $ref: "#/components/schemas/FileMetadata"
      required:
        - key
    PresignedUrlScope:
      type: string
      description: Scope of the presigned URL generation.
      enum:
        - PUBLIC_API # Presigned URL usable in Public API use cases. URL is modified to work with public endpoints
        - INTERNAL # Usable internally in Deuss cluster - pointing directly to S3.
