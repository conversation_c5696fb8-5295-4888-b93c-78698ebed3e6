openapi: 3.0.0
info:
  title: DEUSS KYC API
  version: 1.9.0
  description: API for user and company KYC verification

paths:
  /kyc/user/request:
    post:
      tags:
        - KYC
      summary: Request KYC verification for the user
      description: Initiate KYC verification for the user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  format: uuid
                  example: "123e4567-e89b-12d3-a456-************"
                kyc_provider:
                  type: string
                  example: "partnerKYC"
                callback_url:
                  type: string
                  description: URL for receiving KYC status updates
                  example: "https://deuss.com/api/kyc/callback"
              required:
                - user_id
                - kyc_provider
      responses:
        '201':
          description: KYC request initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  kyc_request_id:
                    type: string
                    format: uuid
                    example: "123e4567-e89b-12d3-a456-************"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"

  /kyc/company/request:
    post:
      tags:
        - KYC
      summary: Request KYC verification for the company
      description: Initiate KYC verification for the company
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                company_id:
                  type: string
                  format: uuid
                  example: "123e4567-e89b-12d3-a456-************"
                kyc_provider:
                  type: string
                  example: "partnerKYC"
                callback_url:
                  type: string
                  description: URL for receiving KYC status updates
                  example: "https://deuss.com/api/kyc/callback"
              required:
                - company_id
                - kyc_provider
      responses:
        '201':
          description: KYC request initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  kyc_request_id:
                    type: string
                    format: uuid
                    example: "123e4567-e89b-12d3-a456-************"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"

  /kyc/user/{kyc_request_id}/status:
    get:
      tags:
        - KYC
      summary: Check KYC status for the user
      description: Retrieve the current status of a KYC verification request for the user
      parameters:
        - name: kyc_request_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
          description: Unique identifier of the user KYC request
      responses:
        '200':
          description: KYC status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [INITIALIZED, PENDING, VERIFIED, DENIED]
                    example: "DENIED"
                  reason:
                    type: string
                    nullable: true
                    example: "Insufficient document clarity"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /kyc/company/{kyc_request_id}/status:
    get:
      tags:
        - KYC
      summary: Check KYC status for the company
      description: Retrieve the current status of a KYC verification request for the company
      parameters:
        - name: kyc_request_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
          description: Unique identifier of the company KYC request
      responses:
        '200':
          description: KYC status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [PENDING, VERIFIED, DENIED, REVIEW_REQUIRED]
                    example: "DENIED"
                  reason:
                    type: string
                    nullable: true
                    example: "Missing company registration document"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /kyc/user/{kyc_request_id}/documents:
    post:
      tags:
        - KYC
      summary: Upload KYC documents, additonal data for the user
      description: Allows users to upload required KYC documents, additional data for verification
      parameters:
        - name: kyc_request_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
          description: Unique identifier of the user KYC request
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                document_type:
                  type: string
                  enum: [PASSPORT, ID_CARD, BANK_STATEMENT]
                  example: "ID_CARD"
                file:
                  type: string
                  format: binary
      responses:
        '201':
          description: Document uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  document_id:
                    type: string
                    format: uuid
                    example: "123e4567-e89b-12d3-a456-************"

  /kyc/company/{kyc_request_id}/documents:
    post:
      tags:
        - KYC
      summary: Upload KYC documents, additional data for the company
      description: Allows companies to upload required KYC documents, additional data for verification
      parameters:
        - name: kyc_request_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
          description: Unique identifier of the company KYC request
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                document_type:
                  type: string
                  enum: [REGISTRATION_CERTIFICATE, SHAREHOLDER_LIST, FINANCIAL_STATEMENT]
                  example: "REGISTRATION_CERTIFICATE"
                file:
                  type: string
                  format: binary
      responses:
        '201':
          description: Document uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  document_id:
                    type: string
                    format: uuid
                    example: "123e4567-e89b-12d3-a456-************"

  /kyc/callback:
    post:
      tags:
        - KYC
      summary: KYC provider callback
      description: Endpoint for receiving KYC verification status from the external KYC provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                kyc_request_id:
                  type: string
                  format: uuid
                  example: "123e4567-e89b-12d3-a456-************"
                status:
                  type: string
                  enum: [VERIFIED, DENIED, REVIEW_REQUIRED]
                  example: "DENIED"
                reason:
                  type: string
                  nullable: true
                  example: "Face mismatch"
      responses:
        '200':
          description: KYC callback processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "KYC status updated"
