openapi: 3.0.0
info:
  title: DEUSS Company Service API
  version: 1.24.2
  description: API for company service for CRUD operations
paths:
  /companies:
    post:
      operationId: create_new_company
      summary: Creates a new company
      description: Creates a new company with the provided details.
      tags:
        - Companies
      requestBody:
        $ref: "#/components/requestBodies/CompanyBody"
      responses:
        '201':
          description: Company has been successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompanyDetailResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
    get:
      operationId: get_companies
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
        - name: name
          in: query
          required: false
          schema:
            type: string
        - name: registrationEntity
          in: query
          required: false
          schema:
            $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"
        - name: verificationState
          in: query
          required: false
          schema:
            $ref: "../company-service-common.yaml#/components/schemas/CompanyVerificationStatus"
        - name: originatorName
          in: query
          required: false
          schema:
            type: string
        - name: walletAddress
          in: query
          required: false
          schema:
            type: string
        - name: sortBy
          in: query
          required: false
          description: "Property to sort by. e.g. name"
          schema:
            type: string
            enum:
              - name
              - verificationState
        - name: sortDirection
          in: query
          required: false
          description: "Sort order"
          schema:
            $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/OrderDirection"
      summary: Retrieve all companies for admin
      description: Fetches a list of all registered companies.
      tags:
        - Companies
      responses:
        '200':
          $ref: "#/components/responses/CompanyAdminPagedResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"

  /companies/{company_id}/status:
    patch:
      operationId: update_companies_status
      summary: Update company status
      description: Updates the verification status of company. Admin only operation.
      tags:
        - Companies
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CompanyStatusUpdateRequest"
      responses:
        '200':
          description: Company details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompanyDetailResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /companies/my:
    get:
      operationId: my_companies
      summary: Retrieve all companies for the user
      description: Fetches a list of all registered companies connected to the user.
      tags:
        - Companies
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      responses:
        '200':
          $ref: "#/components/responses/CompanyUserPagedResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /companies/{company_id}:
    get:
      operationId: get_company_by_id
      summary: Retrieve a specific company
      description: Fetches details of a company by its unique off chain identifier.
      tags:
        - Companies
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      responses:
        '200':
          description: Company details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompanyDetailResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    patch:
      operationId: patch_company
      summary: Patches company with new data from user
      description: Patches company with new data from user
      tags:
        - Companies
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CompanyUpdateRequest"
      responses:
        '200':
          description: Company has been patched successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompanyDetailResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
    delete:
      operationId: delete_company
      summary: Deletes company by id.
      description: Deletes company by id, only not validated companies can be deleted.
      tags:
        - Companies
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      responses:
        '204':
          description: Deleted company successfully
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
  /companies/{company_id}/validation:
    post:
      operationId: validate
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      summary: Sends company with all data to be processed by validation authorities
      tags:
        - Companies
      responses:
        '200':
          description: Company successfully sent
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"


  /companies/{company_id}/wallet:
    post:
      operationId: create_company_wallet
      requestBody: 
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/WalletCreateRequest"
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      summary: Creates a wallet for the company on the blockchain
      tags:
        - Companies
      responses:
        '201':
          description: Wallet created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WalletCreateResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409' :
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
      


  /companies/{company_id}/shareholders:
    post:
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      summary: Add a shareholder to a company
      description: Adds a shareholder to a specified company.
      tags:
        - Shareholders
        - Companies
      operationId: add_shareholder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShareholderCreateRequest"
      responses:
        '201':
          description: Shareholder added to company successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShareholderDetailResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

    get:
      summary: Get all shareholders
      description: Retrieves a list of all shareholders.
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      tags:
        - Shareholders
        - Companies
      operationId: get_shareholders
      responses:
        '200':
          description: Shareholders
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ShareholderDetailResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /companies/{company_id}/documents:
    post:
      summary: Upload a company document
      description: Uploads a document for the specified company.
      tags:
        - Documents
        - Companies
      operationId: upload_company_document
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CompanyDocumentCreateRequest"
      responses:
        '201':
          $ref: "#/components/responses/UploadCompanyDocumentResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

    get:
      summary: Get all documents for a company
      description: Retrieves all uploaded documents for a specified company.
      tags:
        - Documents
        - Companies
      operationId: get_company_documents
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      responses:
        '200':
          $ref: "#/components/responses/GetAllCompanyDocumentsResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /companies/{company_id}/documents/{document_id}:
    delete:
      summary: Delete company document by id
      description: Deletes company document by id.
      tags:
        - Documents
        - Companies
      operationId: delete_company_document
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref: "#/components/parameters/DocumentId"
      responses:
        '204':
          description: Document deleted
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
    patch:
      summary: Creates link for upload of new version of document for company.
      description: Creates link for upload of new version of document for company.
      tags:
        - Documents
        - Companies
      operationId: upload_new_version_of_document
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref: "#/components/parameters/DocumentId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ReUploadCompanyDocumentRequest"
      responses:
        '200':
          $ref: "#/components/responses/ReUploadCompanyDocumentResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
  /companies/{company_id}/documents/{document_id}/download:
    get:
      summary: Generate link for document download.
      description: Generate link for document download.
      tags:
        - Documents
        - Companies
      operationId: generate_company_document_download_link
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref: "#/components/parameters/DocumentId"
      responses:
        '200':
          $ref: "#/components/responses/GenerateDocumentDownloadLinkResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
  /companies/{company_id}/employees:
    post:
      summary: Add a company employee
      description: Add an employee for the specified company.
      tags:
        - Employees
        - Companies
      operationId: add_company_employee
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeCreateRequest"
      responses:
        '201':
          $ref: "#/components/responses/EmployeeCreateResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
    get:
      summary: Get company employees
      description: Get a list of employees
      tags:
        - Employees
        - Companies
      operationId: get_company_employees
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      responses:
        '200':
          $ref: "#/components/responses/CompanyEmployeesResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"

  /companies/{company_id}/employees/{employee_id}:
    delete:
      summary: Delete a company employee
      description: Delete an employee from the specified company.
      tags:
        - Employees
        - Companies
      operationId: delete_company_employee
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref : "#/components/parameters/EmployeeId"
      responses:
        '204':
          description: Employee deleted successfully
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    patch:
      summary: Edit company employee
      description: Edit role of a specific company employee
      tags:
        - Employees
        - Companies
      operationId: edit_company_employee_role
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref : "#/components/parameters/EmployeeId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeUpdateRequest"
      responses:
        '200':
          $ref: "#/components/responses/EmployeeCreateResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /companies/{company_id}/complete-registration:
    post:
      summary: Complete company registration
      description: Finalizes the registration process for the specified company.
      tags:
        - Companies
      operationId: complete_company_registration
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
      responses:
        '204':
          description: Registration completed successfully
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /originators:
    get:
      operationId: get_originators
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      summary: Retrieve all originators
      description: Fetches a list of all registered originators.
      tags:
        - Originators
      responses:
        '200':
          $ref: "#/components/responses/CompanyPagedResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
    post:
      operationId: create_new_originator
      summary: Creates a new Originator
      description: Creates a new originator with the provided details.
      tags:
        - Originators
      requestBody:
        $ref: "#/components/requestBodies/OriginatorBody"
      responses:
        '201':
          description: Originator has been successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OriginatorDetailResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
  /originators/my:
    get:
      operationId: my_originators
      summary: Retrieve all originator companies for the user
      description: Fetches a list of all originator companies connected to the user.
      tags:
        - Originators
      parameters:
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      responses:
        '200':
          $ref: "#/components/responses/CompanyUserPagedResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /originators/{originator_id}:
    get:
      operationId: get_originator_by_id
      summary: Retrieve a specific originator
      description: Fetches details of a originator by its unique off chain identifier.
      tags:
        - Originators
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
      responses:
        '200':
          description: Originator details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OriginatorDetailResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    patch:
      operationId: patch_originator
      summary: Patches originator with new data from user
      description: Patches originator with new data from user
      tags:
        - Originators
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OriginatorUpdateRequest"
      responses:
        '200':
          description: Originator has been patched successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OriginatorDetailResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
    delete:
      operationId: delete_originator
      summary: Deletes originator by id.
      description: Deletes originator by id.
      tags:
        - Originators
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
      responses:
        '204':
          description: Deleted originator successfully
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /originators/{originator_id}/companies:
    get:
      summary: Get originator managed companies
      description: Get a list of companies connected to specific originator
      tags:
        - Companies
        - Originators
      operationId: get_originator_managed_companies
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/Page"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/parameters/PageSize"
      responses:
        '200':
          $ref: "#/components/responses/CompanyPagedResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"

  /originators/{originator_id}/employees:
    post:
      summary: Add an originator employee
      description: Add an employee for the specified originator
      tags:
        - Employees
        - Originators
      operationId: add_originator_employee
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeCreateRequest"
      responses:
        '201':
          $ref: "#/components/responses/EmployeeCreateResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
    get:
      summary: Get company employees
      description: Get a list of employees
      tags:
        - Employees
        - Originators
      operationId: get_originator_employees
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
      responses:
        '200':
          $ref: "#/components/responses/CompanyEmployeesResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"

  /originators/{originator_id}/employees/{employee_id}:
    delete:
      summary: Delete originator employee
      description: Remove an employee for the specified originator.
      tags:
        - Employees
        - Originators
      operationId: delete_originator_employee
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
        - $ref: "#/components/parameters/EmployeeId"
      responses:
        '200':
          $ref: "#/components/responses/OriginatorEmployeeDeleteResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    patch:
      summary: Edit originator employee
      description: Edit role of a specific company employee
      tags:
        - Employees
        - Originators
      operationId: edit_originator_employee_role
      parameters:
        - $ref: "#/components/parameters/OriginatorId"
        - $ref: "#/components/parameters/EmployeeId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeUpdateRequest"
      responses:
        '200':
          $ref: "#/components/responses/EmployeeCreateResponse"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /shareholders/{shareholder_id}:
    delete:
      summary: Delete shareholder
      description: Removes shareholder from system - from company
      tags:
        - Shareholders
      operationId: remove_shareholder
      parameters:
        - $ref: "#/components/parameters/ShareholderId"
      responses:
        '204':
          description: Shareholder removed from company
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"
    patch:
      summary: Patches shareholder data
      description: Updates shareholder data
      tags:
        - Shareholders
      operationId: update_shareholder
      parameters:
        - $ref: "#/components/parameters/ShareholderId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShareholderUpdateRequest"

      responses:
        '200':
          $ref: "#/components/responses/ShareholderResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"


  /companies/{company_id}/users/{user_id}/shareholders:
    post:
      summary: Adds user as shareholder to company
      description: Adds user as shareholder to company
      tags:
        - Shareholders
        - Users
        - Company
      operationId: add_user_as_shareholder
      parameters:
        - $ref: "../company-service-common.yaml#/components/parameters/CompanyId"
        - $ref: "../company-service-common.yaml#/components/parameters/UserId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShareWrapper"
      responses:
        '201':
          description: Shareholder added to company
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        '409':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /validators:
    get:
      operationId: get_validator_list
      summary: Retrieve list of validators
      description: Fetches a list of all available validators that can validate companies.
      tags:
        - Companies
      responses:
        '200':
          description: List of validators retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ValidatorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"

components:
  requestBodies:
    AddressBody:
      description: Address input type
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AddressCreateRequest"
    CompanyBody:
      description: Company input type
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CompanyCreateRequest"
    OriginatorBody:
      description: Originator input type
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/OriginatorCreateRequest"

    CompanySendForValidationRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ValidatorSummary"

  responses:
    GetAllCompanyDocumentsResponse:
      description: Response with documents uploaded for particular company
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GetAllCompanyDocumentsResponse"
    CompanyPagedResponse:
      description: Represents a paginated response containing a list of companies, including the total number of available results and the requested page for admin.
      content:
        application/json:
          schema:
            title: CompanyPagedResponse
            allOf:
              - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"
              - type: object
                properties:
                  companies:
                    type: array
                    description: Fetched companies
                    items:
                      $ref: "#/components/schemas/CompanySummary"
    CompanyUserPagedResponse:
      description: Represents a paginated response containing a list of companies, including the total number of available results and the requested page for user.
      content:
        application/json:
          schema:
            title: CompanyUserPagedResponse
            allOf:
              - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"
              - type: object
                properties:
                  companies:
                    type: array
                    description: Fetched companies
                    items:
                      $ref: "#/components/schemas/CompanyUserSummary"
    CompanyAdminPagedResponse:
      description: Represents a paginated response containing a list of companies, including the total number of available results and the requested page for admin.
      content:
        application/json:
          schema:
            title: CompanyAdminPagedResponse
            allOf:
              - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"
              - type: object
                properties:
                  companies:
                    type: array
                    description: Fetched companies
                    items:
                      $ref: "#/components/schemas/CompanyAdminSummary"
    ShareholderResponse:
      description: Response with data of added shareholder
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ShareholderDetailResponse"
    UploadCompanyDocumentResponse:
      description: Document uploaded successfully
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UploadCompanyDocumentResponse"
    ReUploadCompanyDocumentResponse:
      description: Document re-uploaded successfully
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ReUploadCompanyDocumentResponse"
    EmployeeCreateResponse:
      description: Response with data of added employee
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EmployeeResponse"
    CompanyEmployeesResponse:
      description: Response with data of employees in company
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "#/components/schemas/EmployeeResponse"
    OriginatorEmployeeDeleteResponse:
      description: Response with data of deleted employee
      content:
        application/json:
          schema:
            title: OriginatorEmployeeDeleteResponse
            type: object
            properties:
              isUserOriginatorElsewhere:
                description: Indicates if the user is an originator in another company. Based on this, frontend will remove the ORIGINATOR user role or not.
                type: boolean
                example: true
    GenerateDocumentDownloadLinkResponse:
      description: Response with company document download link
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GenerateDocumentDownloadLinkResponse"

  parameters:
    OriginatorId:
      name: originator_id
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405b
      description: Unique off chain identifier of the originator
    ShareholderId:
      name: shareholder_id
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      description: Unique off chain identifier of the shareholder
    DocumentId:
      name: document_id
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      description: Unique off chain identifier of the shareholder
    EmployeeId:
      name: employee_id
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      description: Unique off chain identifier of the employee

  schemas:
    CompanySummary:
      description: Company entity for listing from admin perspective
      properties:
        company_id:
          type: string
          format: uuid
          description: Unique off chain identifier of the company
          example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
        company_name:
          type: string
          description: Name of the company
          example: Acme Corp
        logo:
          type: string
          format: url
          nullable: true
        verification_state:
          $ref: "../company-service-common.yaml#/components/schemas/CompanyVerificationStatus"
        company_type:
          $ref: "../company-service-common.yaml/#/components/schemas/CompanyType"
        wallet_address:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/WalletAddress"
      required:
        - company_id
        - verification_state
        - company_name
        - company_type

    CompanyUserSummary:
      description: Company entity for listing from user perspective
      properties:
        company_id:
          type: string
          format: uuid
          description: Unique off chain identifier of the company
          example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
        company_name:
          type: string
          description: Name of the company
          example: Acme Corp
        logo:
          type: string
          format: url
          nullable: true
        verification_state:
          $ref: "../company-service-common.yaml#/components/schemas/CompanyVerificationStatus"
        employee_role:
          $ref: "../company-service-common.yaml#/components/schemas/EmployeeRole"
        company_type:
          $ref: "../company-service-common.yaml/#/components/schemas/CompanyType"
        wallet_address:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/WalletAddress"
      required:
        - company_id
        - verification_state
        - company_name
        - employee_role
        - company_type

    CompanyAdminSummary:
      description: Company entity for listing from admin perspective
      properties:
        company_id:
          type: string
          format: uuid
          description: Unique off chain identifier of the company
          example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
        company_name:
          type: string
          description: Name of the company
          example: Acme Corp
        registration_entity:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"
        verification_state:
          $ref: "../company-service-common.yaml/#/components/schemas/CompanyVerificationStatus"
        originator_id:
          $ref: "#/components/schemas/OriginatorId"
        originator_name:
          $ref: "#/components/schemas/OriginatorName"
      required:
        - company_id
        - company_name
        - registration_entity
        - verification_state

    CompanyDetailResponse:
      description: Company response type
      allOf:
        - $ref: "#/components/schemas/CompanyCreateRequest"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/AuditTimestamp"
        - type: object
          properties:
            company_id:
              type: string
              format: uuid
              description: Unique off chain identifier of the company
              example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
            verification_state:
              $ref: "../company-service-common.yaml/#/components/schemas/CompanyVerificationStatus"
            addresses:
              $ref: "#/components/schemas/AddressesResponse"
            employee_role:
              $ref: "../company-service-common.yaml/#/components/schemas/EmployeeRole"
              nullable: true
            types:
              type: array
              items:
                $ref: "../company-service-common.yaml/#/components/schemas/CompanyType"
            headcount:
              $ref: "#/components/schemas/HeadcountCategory"
            annual_turnover_eur:
              $ref: "#/components/schemas/AnnualTurnoverCategory"
            balance_sheet_total_eur:
              $ref: "#/components/schemas/BalanceSheetTotalCategory"
            ownership_status:
              $ref: "#/components/schemas/OwnershipStatus"
            originator_id:
              $ref: "#/components/schemas/OriginatorId"
            validator:
              description: Validator details if the company is being validated
              $ref: "#/components/schemas/ValidatorSummary"
              nullable: true
            last_validator_message:
              type: string
              description: most recent comment about state change
              example: Invalid document was provided.
            wallet_address:
              $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/WalletAddress"
      required:
        - verification_state
        - addresses
        - kyc_company_link
        - types
        - company_id
        - headcount
        - annual_turnover_eur
        - balance_sheet_total_eur
        - ownership_status
    CompanyCreateRequest:
      type: object
      properties:
        company_name:
          type: string
          description: Name of the company
          example: Acme Corp
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
        business_id:
          type: string
          description: Unique business identifier
          example: '12345678'
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
        vat_number:
          type: string
          description: VAT number of the company
          example: CZ12345678
          maxLength: 50
          x-field-extra-annotation: "@NotBlank"
        website:
          type: string
          format: url
          description: Website of the company
          example: https://www.acmecorp.com
          maxLength: 2048
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        registration_date:
          type: string
          format: date
          description: Company establishment date
          x-field-extra-annotation: "@PastOrPresent(message=\"registration_date must be in past or present\")"
          example: '2023-01-01'
        iban:
          type: string
          description: Bank account number
          example: ************************
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
        originator_id:
          $ref: "#/components/schemas/OriginatorId"
        registration_entity:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"
        addresses:
          type: array
          items:
            $ref: "#/components/schemas/AddressCreateRequest"
        headcount:
          $ref: "#/components/schemas/HeadcountCategory"
        annual_turnover_eur:
          $ref: "#/components/schemas/AnnualTurnoverCategory"
        balance_sheet_total_eur:
          $ref: "#/components/schemas/BalanceSheetTotalCategory"
        ownership_status:
          $ref: "#/components/schemas/OwnershipStatus"
        validator:
          description: Validator details if the company is being validated
          $ref: "#/components/schemas/ValidatorSummary"
          nullable: true
        contact_email:
          type: string
          format: email
          maxLength: 255
          description: Contact email for the company
          example: <EMAIL>
          x-field-extra-annotation: "@NotBlank"
        contact_phone:
          type: string
          description: Contact phone number for the company
          maxLength: 255
          example: '+420123456789'
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        legal_entity_id:
          type: string
          description: Legal entity identifier, if applicable
          example: 'LEI1234567890'
          maxLength: 255
          nullable: true
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
      required:
        - company_name
        - business_id
        - registration_date
        - iban
        - registration_entity
        - vat_number
        - headcount
        - annual_turnover_eur
        - balance_sheet_total_eur
        - ownership_status
        - contact_email

    CompanyStatusUpdateRequest:
      type: object
      properties:
        verification_state:
          $ref: "../company-service-common.yaml#/components/schemas/CompanyVerificationStatus"
      required:
        - verification_state

    CompanyUpdateRequest:
      type: object
      properties:
        company_name:
          type: string
          maxLength: 255
          description: Name of the company
          example: Acme Corp
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          nullable: true
        business_id:
          type: string
          description: Unique business identifier
          example: '12345678'
          maxLength: 255
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          nullable: true
        vat_number:
          type: string
          description: VAT number of the company
          example: CZ12345678
          maxLength: 50
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          nullable: true
        website:
          type: string
          format: url
          description: Website of the company
          example: https://www.acmecorp.com
          maxLength: 2048
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          nullable: true
        registration_date:
          type: string
          format: date
          description: Company registration date
          x-field-extra-annotation: "@PastOrPresent(message=\"registration_date must be in past or present\")"
          example: '2023-01-01'
          nullable: true
        iban:
          type: string
          description: Bank account number
          example: ************************
          maxLength: 255
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          nullable: true
        addresses:
          type: array
          items:
            $ref: "#/components/schemas/AddressUpdateRequest"
        headcount:
          $ref: "#/components/schemas/HeadcountCategory"
        annual_turnover_eur:
          $ref: "#/components/schemas/AnnualTurnoverCategory"
        balance_sheet_total_eur:
          $ref: "#/components/schemas/BalanceSheetTotalCategory"
        ownership_status:
          $ref: "#/components/schemas/OwnershipStatus"
        originator_id:
          $ref: "#/components/schemas/OriginatorId"
        validator:
          description: Validator details if the company is being validated
          $ref: "#/components/schemas/ValidatorSummary"
          nullable: true
        contact_email:
          type: string
          format: email
          description: Contact email for the company
          example: <EMAIL>
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        contact_phone:
          type: string
          description: Contact phone number for the company
          example: '+420123456789'
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        legal_entity_id:
          type: string
          description: Legal entity identifier, if applicable
          example: 'LEI1234567890'
          maxLength: 255
          nullable: true
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"

    OriginatorDetailResponse:
      description: Originator response type
      allOf:
        - $ref: "#/components/schemas/OriginatorCreateRequest"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/AuditTimestamp"
        - $ref: "#/components/schemas/AddressCreateRequest"
        - type: object
          properties:
            originator_id:
              type: string
              format: uuid
              description: Unique off chain identifier of the originator
              example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      required:
        - originator_id

    OriginatorCreateRequest:
      type: object
      properties:
        originator_name:
          $ref: "#/components/schemas/OriginatorName"
        registration_date:
          type: string
          format: date
          description: Company registration date
          x-field-extra-annotation: "@PastOrPresent(message=\"registration_date must be in past or present\")"
          example: '2023-01-01'
      allOf:
        - $ref: "#/components/schemas/AddressCreateRequest"
      required:
        - originator_name

    OriginatorUpdateRequest:
      type: object
      properties:
        originator_name:
          $ref: "#/components/schemas/OriginatorName"
        registration_date:
          type: string
          format: date
          description: Company registration date
          x-field-extra-annotation: "@PastOrPresent(message=\"registration_date must be in past or present\")"
          example: '2023-01-01'
      allOf:
        - $ref: "#/components/schemas/AddressUpdateRequest"

    AddressCreateRequest:
      description: Base address object, which contains all necessary data definitions
      type: object
      properties:
        street_name:
          type: string
          description: Street name
          example: Navarova
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
        street_number:
          type: string
          description: Street number
          example: 128/55
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
        city:
          type: string
          description: City name
          example: Prague
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
        postal_code:
          type: string
          description: Postal code
          example: '11000'
          maxLength: 20
          x-field-extra-annotation: "@NotBlank"
        state_region:
          type: string
          description: Region in state
          example: Prague
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
        country:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"
      required:
        - street_name
        - street_number
        - city
        - postal_code
        - state_region
        - country

    AddressUpdateRequest:
      description: Base address object, which contains all necessary data definitions
      type: object
      properties:
        street_name:
          type: string
          description: Street name
          example: Navarova
          maxLength: 255
          nullable: true
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        street_number:
          type: string
          description: Street number
          example: 128/55
          maxLength: 255
          nullable: true
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        city:
          type: string
          description: City name
          example: Prague
          maxLength: 255
          nullable: true
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        postal_code:
          type: string
          description: Postal code
          example: '11000'
          maxLength: 20
          nullable: true
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        state_region:
          type: string
          description: Region in state
          example: Prague
          maxLength: 255
          nullable: true
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        country:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"

    ShareholderCreateRequest:
      type: object
      allOf:
        - $ref: "#/components/schemas/ShareWrapper"
        - type: object
          properties:
            first_name:
              type: string
              maxLength: 255
              x-field-extra-annotation: "@NotBlank"
              example: Minh Tu
            last_name:
              type: string
              maxLength: 255
              x-field-extra-annotation: "@NotBlank"
              example: Pham
            address:
              $ref: "#/components/schemas/AddressCreateRequest"
      required:
        - share
        - first_name
        - last_name
        - address

    ShareholderUpdateRequest:
      type: object
      properties:
        share:
          type: string
          description: Share in company owned by shareholder
          example: 20%
          maxLength: 255
        first_name:
          type: string
          maxLength: 255
          example: Tran Nach
        last_name:
          type: string
          maxLength: 255
          example: Tu
        address:
          $ref: "#/components/schemas/AddressUpdateRequest"

    ShareholderDetailResponse:
      description: Shareholder DTO
      allOf:
        - $ref: "#/components/schemas/ShareholderCreateRequest"
        - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/AuditTimestamp"
        - type: object
          properties:
            shareholder_id:
              type: string
              format: uuid
              description: Unique off chain identifier of the shareholder
              example: a715268c-6b20-4ccc-9779-c6f5e68a577f
            address:
              $ref: "#/components/schemas/AddressDetail"
      required:
        - shareholder_id

    AddressesResponse:
      description: Addresses with off chain id to be used as output
      type: array
      items:
        $ref: "#/components/schemas/AddressDetail"


    CompanyDocument:
      description: Company document uploaded to prove it's validity
      type: object
      properties:
        document_id:
          type: string
          format: uuid
          description: Unique off-chain identifier of the document
          example: 123e4567-e89b-12d3-a456-************
        document_type:
          $ref: "#/components/schemas/DocumentType"
        document_name:
          type: string
          description: Name of the document
          example: bruh.png
        file_type:
          $ref: "#/components/schemas/FileType"
        uploader_id:
          type: string
          format: uuid
          description: Off chain id of user who uploaded this file
          example: df4efd01-6586-4072-8df1-f5d9562e3a1b
        uploaded_at:
          type: string
          format: date-time
          description: DateTime in which was file uploaded
          example: "2023-01-15T10:00:00.000Z"
        dms_path:
          type: string
          format: uri
          example: https://aftermath.example.com/
          description: Path to data management system from file can be downloaded
          deprecated: true
      required:
        - document_id
        - document_name
        - document_type
        - file_type
        - uploader_id
        - uploaded_at
        - dms_path
      example:
        document_id: '123e4567-e89b-12d3-a456-************'
        document_type: 'FINANCIAL_STATEMENT'
        document_name: 'Q4 Report'
        uploader_id: '550e8400-e29b-41d4-a716-************'
        uploaded_at: '2023-01-15T10:00:00.000Z'
        file_type: 'PDF'
        dms_path: 'https://dms.example.com/documents/123.pdf'

    CompanyDocumentCreateRequest:
      description: Company document creation request
      type: object
      properties:
        document_type:
          $ref: "#/components/schemas/DocumentType"
        document_name:
          type: string
          description: Name of the document
        file_type:
          $ref: "#/components/schemas/FileType"
        content_length:
          type: integer
          format: int64
          example: 64
        checksum_sha256:
          description: "SHA256 checksum in base64 format."
          type: string
      required:
        - document_name
        - document_type
        - file_type
      example:
        document_type: 'FINANCIAL_STATEMENT'
        document_name: 'Q4 Report'
        file_type: 'PDF'
    ReUploadCompanyDocumentRequest:
      description: Allow to upload new version of company document.
      type: object
      properties:
        document_name:
          type: string
          description: Name of the document
        file_type:
          $ref: "#/components/schemas/FileType"
        content_length:
          type: integer
          format: int64
          example: 64
        checksum_sha256:
          type: string
      required:
        - document_name
        - file_type
      example:
        document_name: 'Q4 Report'
        file_type: 'PDF'



    OriginatorId:
      type: string
      format: uuid
      description: Unique off chain identifier of the originator, used if company is created by originator
      example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      nullable: true

    ShareWrapper:
      type: object
      properties:
        share:
          type: string
          description: Share in company owned by shareholder
          example: 20%
          maxLength: 255
          x-field-extra-annotation: "@NotBlank"
      required:
        - share

    HeadcountCategory:
      description: The category of the total number of employees of the company for the last accounting period.
      type: string
      example: MEDIUM
      enum:
        - SMALL
        - MEDIUM
        - LARGE

    AnnualTurnoverCategory:
      description: The category of the annual turnover of the company for the last accounting period.
      type: string
      example: MEDIUM
      enum:
        - SMALL
        - MEDIUM
        - LARGE

    BalanceSheetTotalCategory:
      description: The category of the total assets of the company in EUR for the last accounting period.
      type: string
      example: MEDIUM
      enum:
        - SMALL
        - MEDIUM
        - LARGE

    OwnershipStatus:
      description: Ownership status of the company
      type: string
      example: AUTONOMOUS
      enum:
        - AUTONOMOUS
        - PARTNER
        - LINKED

    EmployeeCreateRequest:
      description: Employee creation request
      type: object
      properties:
        user_id:
          type: string
          format: uuid
          description: Unique off chain identifier of the user
          example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
        employee_role:
          $ref: "../company-service-common.yaml/#/components/schemas/EmployeeRole"
        email:
          type: string
          format: email
          maxLength: 255
      required:
        - user_id
        - employee_role
        - email

    EmployeeResponse:
      description: Employee of the company
      type: object
      allOf:
        - $ref: "#/components/schemas/EmployeeCreateRequest"
        - properties:
            employee_id:
              type: string
              format: uuid
              description: Unique off chain identifier of the employee
              example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      required:
        - user_id
        - employee_role
        - employee_id
        - email

    EmployeeUpdateRequest:
      description: Employee update request
      type: object
      properties:
        employee_role:
          $ref: "../company-service-common.yaml/#/components/schemas/EmployeeRole"
      required:
        - employee_role

    VerificationStatus:
      description: Verification status for shareholder (possibly something else)
      type: string
      example: NOT_VERIFIED
      enum:
        - NOT_VERIFIED
        - IN_PROGRESS
        - VERIFIED
        - INVALID

    OriginatorName:
      type: string
      description: Name of the originator
      example: Indicka restaurace s.r.o.
      x-field-extra-annotation: "@NotBlank"

    DocumentType:
      description: Type of uploaded document
      type: string
      example: FINANCIAL_STATEMENT
      enum:
        - FINANCIAL_STATEMENT
        - COMPANY_STATUTES
        - COMPANY_STRUCTURE
        - OTHER

    FileType:
      description: File type of uploaded file
      type: string
      example: PDF
      enum:
        - PNG
        - PDF
    AddressDetail:
      allOf:
        - $ref: "#/components/schemas/AddressCreateRequest"
        - properties:
            address_id:
              type: string
              format: uuid
              description: Unique identifier of address
              example: fd62d1f2-ca68-445a-b542-9d02068da0d9
      required:
        - address_id

    ReUploadCompanyDocumentResponse:
      type: object
      properties:
        document:
          $ref: "#/components/schemas/CompanyDocument"
        dms_path:
          type: string
          format: uri
          example: https://aftermath.example.com/
          description: Path to data management system from file can be downloaded
        dms_path_expiration:
          type: string
          format: duration
          example: PT10M
      required:
        - document
        - dms_path
        - dms_path_expiration

    UploadCompanyDocumentResponse:
      type: object
      properties:
        document:
          $ref: "#/components/schemas/CompanyDocument"
        dms_path:
          type: string
          format: uri
          example: https://aftermath.example.com/
          description: Path to data management system from file can be downloaded
        dms_path_expiration:
          type: string
          format: duration
          example: PT10M
      required:
        - document
        - dms_path
        - dms_path_expiration

    GetAllCompanyDocumentsResponse:
      type: object
      required: [documents, pagination]
      properties:
        documents:
          type: array
          items:
            $ref: "#/components/schemas/CompanyDocument"
        pagination:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"

    ValidatorResponse:
      description: Validator entity representing a validator that can validate companies
      type: object
      properties:
        validator_id:
          type: string
          format: uuid
          description: Unique identifier of the validator
          example: 10098943-27fb-496c-bbeb-c3a7691dff1d
        validator_name:
          type: string
          description: Name of the validator
          example: EasyChange, s.r.o.
        identification_number:
          type: string
          description: Identification number of the validator
          example: 290 44 570
        country_code:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/CountryCode"
      required:
        - validator_id
        - validator_name
        - identification_number
        - country_code

    ValidatorSummary:
      description: Validator entity representing a validator that can validate companies
      type: object
      properties:
        validator_id:
          type: string
          format: uuid
          description: Unique identifier of the validator
          example: 10098943-27fb-496c-bbeb-c3a7691dff1d
        validator_name:
          type: string
          description: Name of the validator
          example: EasyChange, s.r.o.
      required:
        - validator_id
        - validator_name

    GenerateDocumentDownloadLinkResponse:
      type: object
      properties:
        download_link:
          type: string
          format: uri
          example: http://example.com/
        expiration:
          type: string
          format: duration
          example: PT2H15M
      required:
        - download_link
        - expiration
  
    WalletCreateRequest:
      description: Wallet creation request
      type: object
      properties:
        owner_id:
          type: string
          format: uuid
          description: UUID of the user that will own the wallet
          example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      required:
        - owner_id

    WalletCreateResponse:
      description: Wallet creation response
      type: object
      properties:
        wallet_address:
          $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/WalletAddress"
      required:
        - wallet_address
