openapi: 3.0.0
info:
  title: Payment Service API
  version: 0.2.0
  description: API for managing payments between chain and payment provider
tags:
  - name: Payment
    description: Endpoints related to Payments (deals from indexer)
  - name: PaymentProvider
    description: Endpoints related to Payment Provider entity (our record of external provider) in this service
paths:

  /byDealId/{deal_id}:
    get:
      operationId: get_payment_by_deal_id
      summary: Retrieve a payment
      description: TODO
      tags:
        - Payment
      parameters:
        - $ref: "#/components/parameters/DealId"
      responses:
        '200':
          description: TODO
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentDetail"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /providers/{provider_id}:
    get:
      operationId: get_provider_by_id
      summary: Retrieve a payment provider
      tags:
        - PaymentProvider
      parameters:
        - $ref: "#/components/parameters/ProviderId"
      responses:
        '200':
          description: Provider detail
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentProviderDetail"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    patch:
      operationId: patch_provider
      summary: Update a payment provider
      tags:
        - PaymentProvider
      parameters:
        - $ref: "#/components/parameters/ProviderId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentProviderUpdateRequest"
      responses:
        '200':
          description: Payment provider successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentProviderDetail"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    delete:
      operationId: delete_provider_by_id
      summary: Delete a payment provider. Works only if it hasn't been used on any payments.
      tags:
        - PaymentProvider
      parameters:
        - $ref: "#/components/parameters/ProviderId"
      responses:
        '200':
          description: Provider successfully deleted
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /providers:
    post:
      operationId: create_new_provider
      summary: Creates a new payment provider
      tags:
        - PaymentProvider
      requestBody:
        $ref: "#/components/requestBodies/ProviderBody"
      responses:
        '201':
          description: Payment Provider has been successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentProviderDetail"
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
    get:
      operationId: get_providers
      parameters:
        - name: name
          in: query
          required: false
          schema:
            type: string
      summary: Retrieve payment providers (all or filtered)
      tags:
        - PaymentProvider
      responses:
        '200':
          $ref: "#/components/responses/PaymentProviderResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"

components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: Basic authentication for payment provider endpoints

  requestBodies:
    ProviderBody:
      description: Company input type
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/PaymentProviderCreateRequest"

  responses:
    MyPaymentsPagedResponse:
      description: Represents a paginated response containing a list of payments, including the total number of available results and the requested page for admin.
      content:
        application/json:
          schema:
            title: PaymentsPagedResponse
            allOf:
              - $ref: "../../common/common_constructs_openapi.yaml#/components/schemas/PagedResultMetadata"
              - type: object
                properties:
                  payments:
                    type: array
                    description: Current payments
                    items:
                      $ref: "#/components/schemas/PaymentDetail"

    PaymentProviderResponse:
      description: List of found providers
      content:
        application/json:
          schema:
            title: PaymentProviderResponse
            allOf:
              - type: object
                properties:
                  paymentProviders:
                    type: array
                    description: Fetched paymentProviders
                    items:
                      $ref: "#/components/schemas/PaymentProviderDetail"

  schemas:
    PaymentDetail:
      description: Detailed information about a Payment
      type: object
      properties:
        timeout:
          type: string
          format: date-time
          description: Timeout for transferring the actual monetary payment. AKA payment expiration date
          x-field-extra-annotation: "@FutureOrPresent(message=\"registration_date must be in past or future\")"
          example: '2023-01-01'
        currency:
          type: string
          description: Currency of the payment
          example: 'EUR'
        totalPrice:
          type: integer
          format: int64
          description: Total price of the payment
          example: '10000000'
        paymentProvider:
          $ref: "#/components/schemas/PaymentProviderDetail"
        transactionReference:
          type: number
          description: Human readable unique symbol for pairing the actual payment with the Payment entity.
          example: '***************'
        status:
          $ref: "#/components/schemas/PaymentState"
      required:
        - timeout
        - currency
        - totalPrice
        - transactionReference
        - status
        - paymentProvider

    PaymentProviderDetail:
      description: Detail of a Payment Provider
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentProviderBase'
        - type: object
          properties:
            id:
              type: string
              format: uuid
              description: Id in our system
              example: ''
          required: [id]

    PaymentProviderCreateRequest:
      description: Summary of a Payment Provider
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentProviderBase'
        - type: object
          required: [iban, bic, name]

    PaymentProviderUpdateRequest:
      description: Summary of a Payment Provider
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentProviderBase'
        - type: object

    PaymentProviderBase:
      type: object
      properties:
        iban:
          type: string
          description: IBAN of the Payment Provider service.
          example: '************************'
          pattern: '^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$'
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        bic:
          type: string
          description: BIC of provider's account
          example: 'DEUTDEFFXXX'
          pattern: '^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$'
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
        name:
          type: string
          description: Name
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
          example: 'EBSI payment provider'

    PaymentState:
      description: States of payment from the user point of view.
      type: string
      example: PENDING
      enum:
        - NON_EXISTING
        - PENDING
        - PAID
        - NOT_PAID
        - CANCELLED
        - IN_DISPUTE
        - SETTLED

    PaymentStateUpdateRequest:
      type: object
      properties:
        transactionReference:
          type: number
          description: Human readable unique symbol for pairing the actual payment with the Payment entity.
          example: '***************'
      required:
        - transactionReference

  parameters:

    DealId:
      name: deal_id
      in: path
      required: true
      schema:
        type: integer
        format: int64
      description: Unique on chain identifier of a deal (bond transaction).

    ProviderId:
      name: provider_id
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: 106f6bea-24cc-4fd3-bf4f-f60e17f7405a
      description: Unique on chain identifier of a payment provider (third party).
