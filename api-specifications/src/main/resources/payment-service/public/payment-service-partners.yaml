openapi: 3.0.0
info:
  title: DEUSS Payment Service Partners API
  version: 1.0.1
  description: API for the payment service called by third parties on Deuss platform
tags:
  - name: PaymentProviderExternal
    description: Endpoints for updating payment state.
paths:

  /paymentArrived:
    post:
      operationId: payment_arrived
      summary: Called when the Payment provider got a payment from a buyer of a bond.
      tags:
        - PaymentProviderExternal
      parameters:
        - name: Authorization
          in: header
          required: true
          description: Basic authentication header
          schema:
            type: string
            example: "Basic dXNlcm5hbWU6cGFzc3dvcmQ="
      requestBody:
        required: true
        x-codegen-request-body-name: updateRequest
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentStateUpdateRequest"
      responses:
        '200':
          description: ""
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /paymentSent:
    post:
      operationId: payment_sent
      summary: Called when the Payment provider sent a payment to a seller of a bond.
      tags:
        - PaymentProviderExternal
      parameters:
        - name: Authorization
          in: header
          required: true
          description: Basic authentication header
          schema:
            type: string
            example: "Basic dXNlcm5hbWU6cGFzc3dvcmQ="
      requestBody:
        required: true
        x-codegen-request-body-name: updateRequest
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentStateUpdateRequest"
      responses:
        '200':
          description: ""
        '400':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        '401':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        '403':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        '404':
          $ref: "../../common/common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

components:

  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: Basic authentication for payment provider endpoints

  schemas:

    PaymentStateUpdateRequest:
      type: object
      properties:
        transactionReference:
          type: number
          description: Human readable unique symbol for pairing the actual payment with the Payment entity.
          example: '123456789012345'
      required:
        - transactionReference
