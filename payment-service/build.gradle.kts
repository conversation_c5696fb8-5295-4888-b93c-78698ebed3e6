import io.micronaut.gradle.openapi.tasks.OpenApiClientGenerator
import io.micronaut.gradle.openapi.tasks.OpenApiServerGenerator
import java.util.concurrent.CopyOnWriteArrayList

private val javaVersion: String by project

version = "0.1"
group = "cz.deuss"

plugins {
    id(libs.plugins.kotlin.noarg.get().pluginId)
    id(libs.plugins.kotlin.allOpen.get().pluginId)
    id(libs.plugins.kotlin.jvm.get().pluginId)
    id(libs.plugins.google.ksp.get().pluginId)
    id(libs.plugins.gradle.shadow.get().pluginId)
    id(libs.plugins.micronaut.application.get().pluginId)
    id(libs.plugins.micronaut.openapi.get().pluginId)
    alias(libs.plugins.apollogql.codegen)
}

dependencies {
    ksp("io.micronaut.data:micronaut-data-processor")
    ksp("io.micronaut:micronaut-http-validation")
    ksp("io.micronaut.serde:micronaut-serde-processor")
    ksp("io.micronaut.validation:micronaut-validation-processor")
    ksp("io.micronaut.openapi:micronaut-openapi")
    ksp("io.micronaut.security:micronaut-security-annotations")
    implementation(project(":shared:deuss-common"))
    implementation("de.mkammerer:argon2-jvm:2.11")
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime")
    implementation("io.micronaut.serde:micronaut-serde-jackson")
    implementation("io.micronaut.validation:micronaut-validation")
    implementation("io.micronaut.reactor:micronaut-reactor")
    implementation("io.micronaut.reactor:micronaut-reactor-http-client")
    implementation(libs.bundles.kotlin)
    implementation(libs.bundles.micronaut.validation)
    implementation(libs.bundles.micronaut.data)
    implementation(libs.bundles.micronaut.security)
    implementation(libs.bundles.logging)
    implementation(project(":deuss-platform-offchain-framework:framework"))
    implementation(libs.apollogql.runtime)
    implementation(project(":payment-service:blockchain"))
    compileOnly("io.micronaut:micronaut-http-client")
    runtimeOnly("ch.qos.logback:logback-classic")
    runtimeOnly("com.fasterxml.jackson.module:jackson-module-kotlin")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.yaml:snakeyaml")
    testImplementation(libs.bundles.test.base)
    testImplementation(platform(libs.test.containers.bom))
    testImplementation(libs.bundles.test.containers)
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testImplementation(project(":deuss-platform-offchain-framework:test"))
    testImplementation(libs.bundles.test.kotest)
}

application {
    mainClass = "cz.deuss.paymentservice.ApplicationKt"
}
java {
    sourceCompatibility = JavaVersion.toVersion(javaVersion)
}

// Configure indexer client generation
apollo {
    service("indexer") {
        // https://www.apollographql.com/docs/kotlin/advanced/plugin-configuration
        srcDir("src/main/resources/indexer-schema")
        packageName.set("cz.deuss.indexer.client")
        generateOptionalOperationVariables.set(true)
        mapScalar("BigInt", "java.math.BigInteger")
        mapScalar("DateTime", "java.time.LocalDateTime", "cz.deuss.paymentservice.apollo.DateTimeAdapter")
        mapScalarToKotlinString("JSON")
    }
}

graalvmNative.toolchainDetection = false

micronaut {
    version = libs.versions.micronaut.version.get()
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("cz.deuss.paymentservice.*")
    }
    openapi {
        val specsSource = project(":api-specifications").layout.buildDirectory

        server(specsSource.get().dir("payment-service").file("full.yaml").asFile) {
            apiPackageName = "cz.deuss.paymentservice.api"
            modelPackageName = "cz.deuss.paymentservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
        }
        client("company-service", specsSource.get().dir("company-service").file("full.yaml").asFile) {
            apiPackageName = "cz.deuss.companyservice.api"
            modelPackageName = "cz.deuss.companyservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
            // THIS IS CLIENT ID NOT URL. url is configured based on clientId. see properties micronaut.http.services.company-service.urls
            // before changing this value search config files and possibly client filters, which modify behavior.
            clientId = "company-service"
            sortParamsByRequiredFlag = true
        }
        client("payment-provider-external", file("src/main/resources/openapi/payment_provider.yaml")) {
            apiPackageName = "cz.deuss.external.api"
            modelPackageName = "cz.deuss.external.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
            // THIS IS CLIENT ID NOT URL. url is configured based on clientId. see properties micronaut.http.services.file-service.urls
            // before changing this value search config files and possibly client filters, which modify behavior.
            clientId = "payment-provider"
            sortParamsByRequiredFlag = true
        }
    }
}


tasks.named<io.micronaut.gradle.docker.NativeImageDockerfile>("dockerfileNative") {
    jdkVersion = javaVersion
}

noArg {
    annotation("jakarta.persistence.Entity")
}
allOpen {
    annotations("jakarta.persistence.Entity", "jakarta.persistence.MappedSuperclass")
}


tasks {
    // re-build specifications before codegen
    withType<OpenApiServerGenerator>() {
        // Make sure Gradle knows to track this input and run tasks in order
        dependsOn(":api-specifications:build")
    }
    // re-build specifications before codegen
    withType<OpenApiClientGenerator>() {
        // Make sure Gradle knows to track this input and run tasks in order
        dependsOn(":api-specifications:build")
    }

    test {
        testLogging {
            showStandardStreams = true
            showStackTraces = true
            showCauses = true
            showExceptions = true
            events("passed", "skipped", "failed")
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        }
        useJUnitPlatform {
            includeEngines("junit-jupiter", "kotest")
        }
        reports {
            junitXml.outputLocation = file("${project.layout.buildDirectory.get()}/reports/tests/test/xml")
        }

        val failedTests = CopyOnWriteArrayList<String>()

        // Collect failed test names
        afterTest(KotlinClosure2<TestDescriptor, TestResult, Unit>({ desc, result ->
            if (result.resultType == TestResult.ResultType.FAILURE) {
                failedTests.add("${desc.className?.substringAfterLast('.')}.${desc.name}")
            }
        }))

        // Print them after the full suite runs
        afterSuite(KotlinClosure2<TestDescriptor, TestResult, Unit>({ desc, _ ->
            if (desc.parent == null && failedTests.isNotEmpty()) {
                println("\n=== FAILED TESTS ===")
                failedTests.forEach { println(it) }
            }
        }))
    }
}
