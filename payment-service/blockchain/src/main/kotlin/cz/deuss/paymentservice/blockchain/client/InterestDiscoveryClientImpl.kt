package cz.deuss.paymentservice.blockchain.client

import cz.deuss.paymentservice.blockchain.config.BlockchainConfiguration
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import deuss.contract.payment.InterestDiscovery
import io.ethers.providers.middleware.Middleware
import io.ethers.signers.PrivateKeySigner
import jakarta.inject.Singleton
import java.math.BigInteger

/**
 * Implementation of InterestDiscoveryClient that interacts with the InterestDiscovery smart contract
 * to manage market offers on the blockchain.
 */
@Singleton
class InterestDiscoveryClientImpl(
    private val middleware: Middleware,
    private val blockchainConfiguration: BlockchainConfiguration,
) : InterestDiscoveryClient {

    private val logger = DeussLogger.semanticLogger(InterestDiscoveryClientImpl::class)

    private val adminSigner: PrivateKeySigner by lazy {
        PrivateKeySigner(blockchainConfiguration.adminPrivateKey)
    }

    private val interestDiscovery: InterestDiscovery by lazy {
        InterestDiscovery(middleware, blockchainConfiguration.interestDiscoveryAddress)
    }

    override fun settleDeal(dealId: BigInteger) {
        try {
            logger.message("Executing settleDeal transaction for dealId: $dealId").info()

            // execute the transaction to settle the deal
            val pendingTransaction = interestDiscovery.settleDeal(dealId)
                .send(adminSigner)
                .sendAwait()
                .unwrapOrElse {
                    throw InterestDiscoveryException("Failed to execute settleDeal transaction for dealId: $dealId")
                }

            // wait for the transaction to be mined
            pendingTransaction.awaitInclusion().unwrapOrElse {
                throw InterestDiscoveryException("Failed to wait for transaction to be mined for dealId: $dealId")
            }

            logger.message("Successfully executed settleDeal transaction for dealId: $dealId").info()
        } catch (e: Exception) {
            // This now catches other exceptions like network issues, etc.
            val genericErrorMessage =
                "A non-contract error occurred while executing settleDeal transaction for dealId: $dealId"
            logger.message(genericErrorMessage).throwable(e).error()
            throw InterestDiscoveryException(genericErrorMessage, e)
        }
    }
}
