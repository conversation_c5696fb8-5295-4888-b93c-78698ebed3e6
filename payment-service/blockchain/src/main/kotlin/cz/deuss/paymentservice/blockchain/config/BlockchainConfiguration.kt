package cz.deuss.paymentservice.blockchain.config

import io.ethers.core.types.Address
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("deuss.blockchain.contracts")
data class BlockchainConfiguration @ConfigurationInject constructor(
    val interestDiscovery: String,
    val adminPrivateKey: String,
) {
    val interestDiscoveryAddress: Address
        get() = Address(interestDiscovery)

}
