package cz.deuss.paymentservice.blockchain.client

import java.math.BigInteger

/**
 * Client interface for interest discovery blockchain operations.
 */
interface InterestDiscoveryClient {

    /**
     * Settles a deal based on its status.
     * @param dealId ID of the deal to settle
     */
    fun settleDeal(dealId: BigInteger)

}

/**
 * Exception thrown when interest discovery operations fail on the blockchain.
 */
class InterestDiscoveryException(message: String, cause: Throwable? = null) : Exception(message, cause)