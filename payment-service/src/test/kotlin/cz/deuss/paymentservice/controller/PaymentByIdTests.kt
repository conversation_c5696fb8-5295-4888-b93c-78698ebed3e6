package cz.deuss.paymentservice.controller

import cz.deuss.paymentservice.BasePaymentServiceTest
import cz.deuss.paymentservice.api.model.PaymentDetail
import cz.deuss.platform.offchain.framework.authentication.Role
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PaymentByIdTests : BasePaymentServiceTest() {

    @Test
    fun `fetch company by id`() {

        val request = HttpRequest
            .GET<Unit>("/byDealId/$PAYMENT_DEAL_ID")
            .bearerAuth(generateToken(roles = setOf(Role.Enum.USER)))
        val response = exposedClient.exchange(request, PaymentDetail::class.java)

        assertThat(response.status).isEqualTo(HttpStatus.OK)
        assertThat(response.body).isNotNull

        val detail = response.body.get()
        assertThat(detail.totalPrice).isEqualTo(1234L)
        assertThat(detail.paymentProvider.name).isEqualTo("EBSI test provider")
    }

    companion object {
        private const val PAYMENT_DEAL_ID = 54321
    }
}
