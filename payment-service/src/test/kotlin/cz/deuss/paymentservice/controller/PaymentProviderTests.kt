package cz.deuss.paymentservice.controller

import cz.deuss.paymentservice.BasePaymentServiceTest
import cz.deuss.paymentservice.api.model.PaymentProviderDetail
import cz.deuss.platform.offchain.framework.authentication.Role
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PaymentProviderTests : BasePaymentServiceTest() {

    @Test
    fun `fetch payment provider by id`() {
        val request = HttpRequest
            .GET<Unit>("/providers/$PAYMENT_PROVIDER_ID")
            .bearerAuth(generateToken(roles = setOf(Role.Enum.ADMIN)))
        val response = exposedClient.exchange(request, PaymentProviderDetail::class.java)

        assertThat(response.status).isEqualTo(HttpStatus.OK)
        assertThat(response.body).isNotNull

        val detail = response.body.get()
        assertThat(detail.name).isEqualTo("EBSI test provider")
        assertThat(detail.iban).isEqualTo("TESTIBAN")
    }

    companion object {
        private const val PAYMENT_PROVIDER_ID = "fb4ca36f-f312-4497-956a-a687c590b93a"
    }
}
