package cz.deuss.paymentservice

import com.fasterxml.jackson.databind.ObjectMapper
import cz.deuss.paymentservice.database.repository.PaymentRepository
import cz.deuss.platform.offchain.test.BaseApplicationTest
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.annotation.Sql
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Inject
import jakarta.inject.Singleton
import org.junit.jupiter.api.TestInstance

@MicronautTest(environments = ["test"], rebuildContext = true, transactional = false)
@Sql(scripts = ["classpath:sql/base_init.sql"], phase = Sql.Phase.BEFORE_EACH)
@Sql(scripts = ["classpath:sql/clear_database.sql"], phase = Sql.Phase.AFTER_EACH)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@MockBean
abstract class BasePaymentServiceTest : BaseApplicationTest("payment-service-db") {

    @Inject
    protected lateinit var paymentRepository: PaymentRepository

    @Inject
    protected lateinit var databaseUtils: DatabaseUtils

    @Inject
    protected lateinit var objectMapper: ObjectMapper

    protected fun <T> inTransaction(block: () -> T): T = databaseUtils.runInTransaction(block)

    @Singleton
    open class DatabaseUtils {

        @Transactional
        open fun <T> runInTransaction(block: () -> T): T {
            return block.invoke()
        }
    }
}
