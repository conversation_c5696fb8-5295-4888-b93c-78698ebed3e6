insert into public.payment_provider(id, iban, name, bic, created, last_edit)
values ('fb4ca36f-f312-4497-956a-a687c590b93a', 'TESTIBAN',
        'EBSI test provider', 'test bic', now(), now());
values ('fb4ca36f-f312-4497-956a-a687c590b93b', 'TESTIBAN2',
        'EBSI test provider 2', 'test bic 2', now(), now());


insert into public.payment(id, payment_provider_id, transaction_reference, total_price, currency, timeout, status, created, last_edit, deal_id,
                           buyer_id, seller_id, buyer_iban, seller_iban, buyer_company_id, seller_company_id, created_at)
values ('d960e889-f7fb-48dd-9274-b2dc64b4f532', 'fb4ca36f-f312-4497-956a-a687c590b93a','12345678', 1234, 'EUR', now(),
        'PENDING', now(), now(),54321, 'abcdef', 'abcd', 'CZ92345678', 'CZ92345678',
        '55b96502-8fe1-4fb7-b515-01900853ec9d', '6b73a649-6dd0-4f6d-be32-c8fa2d030586', now());
