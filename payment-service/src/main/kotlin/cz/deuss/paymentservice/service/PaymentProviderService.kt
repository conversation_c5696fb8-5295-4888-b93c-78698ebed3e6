package cz.deuss.paymentservice.service

import cz.deuss.paymentservice.api.model.PaymentProviderCreateRequest
import cz.deuss.paymentservice.api.model.PaymentProviderDetail
import cz.deuss.paymentservice.api.model.PaymentProviderUpdateRequest
import cz.deuss.paymentservice.database.model.PaymentProvider
import cz.deuss.paymentservice.database.repository.PaymentProviderRepository
import cz.deuss.paymentservice.database.repository.PaymentRepository
import cz.deuss.paymentservice.mappers.toDetail
import cz.deuss.paymentservice.mappers.toEntity
import cz.deuss.paymentservice.mappers.updateEntity
import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.platform.offchain.framework.validation.Invalid
import cz.deuss.platform.offchain.framework.validation.Valid
import cz.deuss.platform.offchain.framework.validation.ValidationResult
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.util.UUID
import kotlin.jvm.optionals.getOrNull


@Singleton
open class PaymentProviderService(
    private val paymentProviderRepository: PaymentProviderRepository,
    private val paymentRepository: PaymentRepository,
) {
    fun findPaymentProvider(paymentProviderId: UUID): PaymentProvider? =
        paymentProviderRepository.findById(paymentProviderId).getOrNull()
    
    @Transactional(readOnly = true)
    open fun getProviders(name: String?): List<PaymentProvider> {
        return if (name != null) {
            paymentProviderRepository.findByNameContainingIgnoreCase(name)
        } else {
            paymentProviderRepository.findAll()
        }
    }

    @Transactional
    open fun insertNewPaymentProvider(
        paymentProviderCreate: PaymentProviderCreateRequest,
    ): PaymentProviderDetail {

        val paymentProvider = paymentProviderCreate.toEntity()

        return paymentProviderRepository.saveAndFlush(paymentProvider).toDetail()
    }

    @Transactional
    open fun updatePaymentProvider(
        paymentProviderId: UUID,
        paymentProviderUpdate: PaymentProviderUpdateRequest,
    ): ValidationResult<ProviderNotFound, PaymentProviderDetail> {

        val paymentProvider = findPaymentProvider(paymentProviderId)
            ?: return Invalid(ProviderNotFound("PaymentProvider#$paymentProviderId not found"))

        val updatedEntity = paymentProviderRepository.saveAndFlush(paymentProvider.updateEntity(paymentProviderUpdate))

        return Valid(updatedEntity.toDetail())
    }

    @Transactional
    open fun deleteProvider(paymentProviderId: UUID) {
        if (paymentRepository.existsByPaymentProviderId(paymentProviderId)) {
            paymentProviderRepository.deleteById(paymentProviderId)
        } else {
            throw BadRequestException("Cannot delete PaymentProvider#$paymentProviderId. It's added to some payments.")
        }
    }
}

@JvmInline
value class ProviderNotFound(val description: String)
