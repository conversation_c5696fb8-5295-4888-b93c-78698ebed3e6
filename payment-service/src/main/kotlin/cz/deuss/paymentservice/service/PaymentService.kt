package cz.deuss.paymentservice.service

import cz.deuss.external.api.model.Currency
import cz.deuss.indexer.client.GetDealsSinceQuery
import cz.deuss.paymentservice.api.model.PaymentStateUpdateRequest
import cz.deuss.paymentservice.database.model.Payment
import cz.deuss.paymentservice.database.repository.PaymentProviderRepository
import cz.deuss.paymentservice.database.repository.PaymentRepository
import cz.deuss.paymentservice.mappers.toDomain
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.platform.offchain.framework.validation.Invalid
import cz.deuss.platform.offchain.framework.validation.Valid
import cz.deuss.platform.offchain.framework.validation.ValidationResult
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.math.BigDecimal
import java.util.UUID
import kotlin.random.Random

@Singleton
open class PaymentService(
    private val paymentRepository: PaymentRepository,
    private val paymentProviderRepository: PaymentProviderRepository,
) {
    private val logger = DeussLogger.semanticLogger(this::class)

    @Transactional
    open fun updateStatus(update: PaymentStateUpdateRequest, newStatus: Payment.Status): ValidationResult<UpdatePaymentStatusError, Payment> {
        logger.action("UpdatePaymentStatus").started()
            .param("transactionReference", update.transactionReference.toString())
            .param("newStatus", newStatus.name).info()

        val payment = paymentRepository.findByTransactionReference(update.transactionReference.toLong())
            ?: return Invalid(UpdatePaymentStatusError(" PaymentReference#${update.transactionReference} not found"))

        payment.status = newStatus
        val updatedPayment = paymentRepository.update(payment)
        logger.action("UpdatePaymentStatus").finished()
            .param("transactionReference", update.transactionReference.toString())
            .param("newStatus", newStatus.name).info()
        return Valid(updatedPayment)
    }

    fun createPaymentEntity(deal: GetDealsSinceQuery.MarketDeal): Payment {
        // on-chain company ids
        val buyerId = deal.marketOffer.owner
        val sellerId = deal.createdBy

        // TODO placeholder, needs to be another function - waiting on david
        // TODO once we get kafka, store pairs companyId,onchainId in a table in this service
//                val buyerCompanyData = companyServiceClient.getCompanyById(buyerId)
//                val sellerCompanyData = companyServiceClient.getCompanyById(sellerId)

        val payment = Payment(
            dealId = deal.dealId,
            buyerId = buyerId,
            sellerId = sellerId,
            totalPrice = BigDecimal(deal.price), // TODO Why tf is price in indexer BigInt???
            currency = Currency.fromValue(deal.marketOffer.currency),
            timeout = deal.expiry,
            status = deal.status.toDomain(),
            transactionReference = getRandomLong10Digits(),
            buyerIban = "CZBUYER_IBAN_TEST", //buyerCompanyData.iban,
            buyerCompanyId = UUID.randomUUID(), //buyerCompanyData.companyId,
            sellerIban = "CZSELLER_IBAN_TEST", // sellerCompanyData.iban,
            sellerCompanyId = UUID.randomUUID(), //sellerCompanyData.companyId,
            createdAt = deal.createdAt,
            // todo temporary solution because for now we only have one PP
            paymentProvider = paymentProviderRepository.findFirst(),
        )
        return paymentRepository.save(payment)
    }

    fun getRandomLong10Digits(): Long {
        return Random.nextLong(1_000_000_000L, 10_000_000_000L)
    }

}

@JvmInline
value class UpdatePaymentStatusError(val description: String)
