package cz.deuss.paymentservice.service

import cz.deuss.paymentservice.client.paymentProvider.EbsiPaymentProviderClient
import cz.deuss.paymentservice.client.paymentProvider.EbsiResponse
import cz.deuss.paymentservice.database.model.Payment
import cz.deuss.paymentservice.mappers.toTransactionDto
import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import jakarta.inject.Singleton
import jakarta.transaction.Transactional


@Singleton
open class PaymentProviderClientService(
    private val paymentProviderClient: EbsiPaymentProviderClient,
) {
    private val logger = DeussLogger.semanticLogger(this::class)

    open fun createPayment(payment: Payment) {
        logger.info { "Payment#${payment.id} is being created in payment provider" }

        val response = paymentProviderClient.createPayment(payment.toTransactionDto())

        if (response == null) {
            logger.error { "Payment creation response was null" }
            return
        }
        if (response.success) {
            logger.info { "Payment#${payment.id} was successfully created in Payment Provider. Response: $response" }
        } else {
            logger.error { "Payment#${payment.id} was not successfully created in Payment Provider. Response: $response" }
        }
    }

    // todo create endpoint that calls this
    open fun validatePayment(payment: Payment) {
        logger.info { "Payment#${payment.id} is being validated in payment provider" }

        val response = paymentProviderClient.validatePayment(payment.toTransactionDto())

        if (response == null) {
            logger.error { "Payment validation response was null" }
            return
        }

        if (response.success) {
            logger.info { "Payment#${payment.id} was successfully validated in Payment Provider. Response: $response" }
        } else {
            logger.error { "Payment#${payment.id} was not successfully validated in Payment Provider. Response: $response" }
        }
    }

    open fun getPaymentStatus(reference: String): EbsiResponse? {
        logger.debug { "Getting status from Payment Provider api" }
        return paymentProviderClient.getPaymentStatus(reference)
    }
}
