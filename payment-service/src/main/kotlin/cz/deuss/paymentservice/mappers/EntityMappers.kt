package cz.deuss.paymentservice.mappers

import cz.deuss.paymentservice.api.model.PaymentDetail
import cz.deuss.paymentservice.api.model.PaymentProviderCreateRequest
import cz.deuss.paymentservice.api.model.PaymentProviderDetail
import cz.deuss.paymentservice.api.model.PaymentProviderUpdateRequest
import cz.deuss.paymentservice.database.model.Payment
import cz.deuss.paymentservice.database.model.PaymentProvider


fun Payment.toDetail(): PaymentDetail {
    return PaymentDetail(
        timeout = this.timeout,
        currency = this.currency.toString(),
        transactionReference = this.transactionReference.toBigDecimal(),
        totalPrice = this.totalPrice.toLong(),
        status = this.status.toApi(),
        paymentProvider = this.paymentProvider.toDetail()
    )
}

fun PaymentProvider.updateEntity(summary: PaymentProviderUpdateRequest): PaymentProvider {
    summary.bic?.let { this.bic = it }
    summary.iban?.let { this.iban = it }
    summary.name?.let { this.name = it }
    return this
}

fun PaymentProvider.toDetail(): PaymentProviderDetail {
    return PaymentProviderDetail(
        iban = this.iban,
        name = this.name,
        bic = this.bic,
        id = this.id,
    )
}

fun PaymentProviderCreateRequest.toEntity(): PaymentProvider {
    return PaymentProvider(
        iban = this.iban,
        name = this.name,
        bic = this.bic,
    )
}
