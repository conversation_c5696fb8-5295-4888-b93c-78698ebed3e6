package cz.deuss.paymentservice.mappers

import cz.deuss.external.api.model.TransactionDto
import cz.deuss.paymentservice.database.model.Payment

fun Payment.toTransactionDto(): TransactionDto {
    return TransactionDto(
        senderIban = this.buyerIban,
        senderBic = null,
        receiverIban = this.sellerIban,
        receiverBic = null,
        amount = this.totalPrice.toDouble(),
        currency = this.currency,
        transactionReference = this.transactionReference.toString(),
    )
}
