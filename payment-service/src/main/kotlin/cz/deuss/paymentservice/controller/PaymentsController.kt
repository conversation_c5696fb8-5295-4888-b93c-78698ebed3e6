package cz.deuss.paymentservice.controller

import cz.deuss.paymentservice.api.PaymentApi
import cz.deuss.paymentservice.api.model.PaymentDetail
import cz.deuss.paymentservice.client.company.CompanyServiceClient
import cz.deuss.paymentservice.database.repository.PaymentRepository
import cz.deuss.paymentservice.mappers.toDetail
import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.authentication.AuthenticationService
import cz.deuss.platform.offchain.framework.database.findByIdOrNull
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import io.micronaut.openapi.visitor.security.SecurityRule
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import java.math.BigDecimal
import java.util.UUID


@ExposedController
@ExecuteOn(TaskExecutors.IO)
@Secured(SecurityRule.IS_AUTHENTICATED)
open class PaymentsController(
    private val paymentRepository: PaymentRepository,
    private val companyServiceClient: CompanyServiceClient,
    private val authenticationService: AuthenticationService,
    private val authUserId: () -> AuthenticatedUserId,
): PaymentApi {

    override fun getPaymentByDealId(dealId: Long): PaymentDetail {
        val payment = paymentRepository.findByDealId(dealId)
            ?: throw NotFoundException("Payment not found")

        // todo users rights to the payment (its company):

        // see if the logged user has rights to view (is an employee of the deal's company)
        // todo skip this for admins?
//        val resp = companyServiceClient
//            .getCompanyDataWithUserContext(authUserId().id, payment.buyerCompanyId)
//            .toDomain()
//
//        when (resp) {
//            is CompanyEmployeeFetchStatus.CompanyNotFoundError -> throw NotFoundException("Company not found")
//            is CompanyEmployeeFetchStatus.UserIsNotEmployeeError -> throw ForbiddenException("User doesn't have rights to view this deal")
//            is CompanyEmployeeFetchStatus.UserNotEmployedAtCompanyError -> throw ForbiddenException("User doesn't have rights to view this deal")
//            is CompanyEmployeeFetchStatus.CompanyWithUserRole -> {
//                return PaymentDetail(
//                    timeout = payment.timeout,
//                    currency = payment.currency.toString(),
//                    message = payment.transactionReference.toString(),
//                    totalPrice = payment.totalPrice.toLong(),
//                    status = payment.status.toApi(),
//                    ppIban = paymentProviderIban,
//                )
//            }
//        }

        return payment.toDetail()
    }
}
