package cz.deuss.paymentservice.controller


import cz.deuss.paymentservice.api.PaymentProviderApi
import cz.deuss.paymentservice.api.model.PaymentProviderCreateRequest
import cz.deuss.paymentservice.api.model.PaymentProviderDetail
import cz.deuss.paymentservice.api.model.PaymentProviderResponse
import cz.deuss.paymentservice.api.model.PaymentProviderUpdateRequest
import cz.deuss.paymentservice.mappers.toDetail
import cz.deuss.paymentservice.service.PaymentProviderService
import cz.deuss.paymentservice.service.ProviderNotFound
import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.platform.offchain.framework.logging.SemanticLogger
import cz.deuss.platform.offchain.framework.validation.Invalid
import cz.deuss.platform.offchain.framework.validation.Valid
import io.micronaut.http.HttpResponse
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import java.util.UUID


@ExposedController
@ExecuteOn(TaskExecutors.BLOCKING)
@Secured(value = [Role.ADMIN])
open class PaymentProviderController(
    private val paymentProviderService: PaymentProviderService,
) : PaymentProviderApi {

    override fun getProviderById(providerId: UUID): PaymentProviderDetail {
        logger.info { "Getting PaymentProvider#$providerId" }
        val pp = paymentProviderService.findPaymentProvider(providerId)
            ?: throw NotFoundException("PaymentProvider#$providerId not found")

        return pp.toDetail()
    }

    override fun getProviders(name: String?): PaymentProviderResponse {
        logger.info { "Getting Payment providers, parameter name: $name" }
        return PaymentProviderResponse(
            paymentProviderService
                .getProviders(name)
                .map { it.toDetail() }
        )
    }

    override fun patchProvider(
        providerId: UUID,
        paymentProviderUpdateRequest: PaymentProviderUpdateRequest
    ): PaymentProviderDetail {
        logger.info { "Patching PaymentProvider#$providerId, new values $paymentProviderUpdateRequest" }

        when (val result = paymentProviderService.updatePaymentProvider(providerId, paymentProviderUpdateRequest)) {
            is Valid -> return result.data
            is Invalid<ProviderNotFound, *> -> throw NotFoundException("PaymentProvider#$providerId not found")
        }
    }

    override fun createNewProvider(paymentProviderCreateRequest: PaymentProviderCreateRequest): HttpResponse<PaymentProviderDetail> {
        val res = paymentProviderService.insertNewPaymentProvider(paymentProviderCreateRequest)
        return HttpResponse.created(res)
    }

    override fun deleteProviderById(providerId: UUID) {
        logger.info { "Deleting PaymentProvider#$providerId" }
        paymentProviderService.deleteProvider(providerId)
    }

    private val logger: SemanticLogger = DeussLogger.semanticLogger(this::class)
}
