package cz.deuss.paymentservice.controller


import cz.deuss.paymentservice.service.PaymentService
import cz.deuss.paymentservice.api.PaymentProviderExternalApi
import cz.deuss.paymentservice.api.model.PaymentStateUpdateRequest
import cz.deuss.paymentservice.blockchain.client.InterestDiscoveryClient
import cz.deuss.paymentservice.database.model.Payment
import cz.deuss.paymentservice.service.Argon2Service
import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.exceptions.ForbiddenException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.platform.offchain.framework.logging.SemanticLogger
import cz.deuss.platform.offchain.framework.validation.Invalid
import cz.deuss.platform.offchain.framework.validation.Valid
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.http.annotation.Header
import io.micronaut.openapi.visitor.security.SecurityRule
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured

@ConfigurationProperties("payment-provider.credentials")
class PaymentProviderCredentialsConfig {
    lateinit var username: String
    lateinit var passwordHash: String
}

@ExposedController
@ExecuteOn(TaskExecutors.BLOCKING)
open class PaymentProviderExternalController(
    private val paymentService: PaymentService,
    private val argon2Service: Argon2Service,
    private val credentialsConfig: PaymentProviderCredentialsConfig,
    private val interestDiscoveryClient: InterestDiscoveryClient,
) : PaymentProviderExternalApi {

    @Secured(SecurityRule.IS_ANONYMOUS)
    override fun paymentArrived(
        @Header("Authorization") authorization: String,
        updateRequest: PaymentStateUpdateRequest,
    ) {
        logger.action("AuthenticateThirdParty").started().param("transactionReference", updateRequest.transactionReference.toString()).info()
        authorize(authorization)

        // todo somehow check actual state?
        val updateResult = paymentService.updateStatus(updateRequest, Payment.Status.PAID)

        when (updateResult) {
            is Valid -> logger.action("Update payment status").finished().info()
            is Invalid -> {
                logger.action("Update payment status").failed().param("description", updateResult.error.description).info()
                throw NotFoundException("Authorization failed")
            }
        }

        interestDiscoveryClient.settleDeal(updateResult.data.dealId)
        // TODO implement cases when the deal cannot be settled
    }

    @Secured(SecurityRule.IS_ANONYMOUS)
    override fun paymentSent(
        @Header("Authorization") authorization: String,
        updateRequest: PaymentStateUpdateRequest,
    ) {
        logger.action("UpdatePaymentStatus").started().param("transactionReference", updateRequest.transactionReference.toString()).info()
        authorize(authorization)

        // todo somehow check actual state?
        val updateResult = paymentService.updateStatus(updateRequest, Payment.Status.SETTLED)

        when (updateResult) {
            is Valid -> logger.action("UpdatePaymentStatus").finished().info()
            is Invalid -> {
                logger.action("UpdatePaymentStatus").failed().param("description", updateResult.error.description).info()
                throw NotFoundException("Authorization failed")
            }
        }

        interestDiscoveryClient.settleDeal(updateResult.data.dealId)
        // TODO implement cases when the deal cannot be settled
    }

    fun authorize(authorization: String) {
        logger.action("AuthenticateThirdParty").started().info()
        val authResult = argon2Service.authenticateBasicAuth(
            authorization,
            expectedUsername = credentialsConfig.username,
            expectedPasswordHash = credentialsConfig.passwordHash
        )
        when (authResult) {
            is Valid -> logger.action("AuthenticateThirdParty").finished().info()
            is Invalid -> {
                logger.action("AuthenticateThirdParty").failed().param("description", authResult.error.description).info()
                throw ForbiddenException("Authorization failed")
            }
        }
    }

    private val logger: SemanticLogger = DeussLogger.semanticLogger(this::class)
}
