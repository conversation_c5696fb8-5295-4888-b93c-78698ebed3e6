package cz.deuss.paymentservice.scheduled

import com.apollographql.apollo.api.Optional
import cz.deuss.indexer.client.GetDealsSinceQuery
import cz.deuss.paymentservice.service.PaymentService
import cz.deuss.paymentservice.client.indexer.IndexerClient
import cz.deuss.paymentservice.database.model.Payment
import cz.deuss.paymentservice.database.repository.PaymentRepository
import cz.deuss.paymentservice.service.PaymentProviderClientService
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import io.micronaut.context.annotation.Requires
import io.micronaut.scheduling.annotation.Scheduled
import jakarta.inject.Singleton
import kotlinx.coroutines.runBlocking


@Singleton
@Requires(notEnv = ["test"])
class ChainDataChecker(
    private val paymentRepository: PaymentRepository,
    private val paymentService: PaymentService,
    private val indexerClient: IndexerClient,
    private val paymentProviderClientService: PaymentProviderClientService,
) {
    private val logger = DeussLogger.semanticLogger(this::class)

    /**
     * Check for new payments on chain and save them locally
     * TODO consider using gql subscriptions to avoid DDoSing the chain
     */
    @Scheduled(fixedDelay = "5s")
    fun checkForNewPayments() {
        // Query all results since the latest payment in DB
        val since = paymentRepository.findNewestPayment()?.createdAt
        val q = if (since != null) Optional.present(since) else Optional.Absent

        runBlocking {
            // fetch fresh batch of data
            val result = indexerClient.query(GetDealsSinceQuery(q)).execute()

            logger.info { "Got new payments from indexer: ${result.data?.marketDeals?.size}" }
            if (result.hasErrors()) {
                logger.error { "Got errors from indexer: ${result.errors}" }
            }
            if (result.exception != null) {
                logger.error(result.exception) { "Got exception from indexer: ${result.exception?.message}" }
            }

            val newOnchainDeals = result.data?.marketDeals.nullIfEmpty() ?: return@runBlocking

            newOnchainDeals.forEach { deal ->
                try {
                    val payment = paymentService.createPaymentEntity(deal)
                    if (payment.status != Payment.Status.PENDING) {
                        logger.warn { "Payment#${payment.id} in ${payment.status} state, not sending to payment provider" }
                    } else {
                        paymentProviderClientService.createPayment(payment)
                    }
                } catch (e: Exception) {
                    logger.error(e) { "Got exception while creating payment from indexer deal: $deal" }
                }
            }
        }
    }

    fun <T : Collection<*>> T?.nullIfEmpty() = if (this.isNullOrEmpty()) null else this
}
