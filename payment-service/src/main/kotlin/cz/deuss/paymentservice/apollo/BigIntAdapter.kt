package cz.deuss.paymentservice.apollo

import com.apollographql.apollo.api.Adapter
import com.apollographql.apollo.api.CustomScalarAdapters
import com.apollographql.apollo.api.json.JsonReader
import com.apollographql.apollo.api.json.JsonWriter
import java.math.BigInteger

object BigIntAdapter : Adapter<BigInteger> {
    override fun from<PERSON><PERSON>(reader: <PERSON>sonRead<PERSON>, customScalarAdapters: CustomScalarAdapters): BigInteger {
        return when (val value = reader.nextString()) {
            null -> throw IllegalArgumentException("BigInt value cannot be null")
            else -> BigInteger(value)
        }
    }

    override fun to<PERSON><PERSON>(
        writer: JsonWriter,
        customScalarAdapters: CustomScalarAdapters,
        value: BigInteger
    ) {
        writer.value(value.toString())
    }
}
