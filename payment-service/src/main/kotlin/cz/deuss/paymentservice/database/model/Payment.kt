package cz.deuss.paymentservice.database.model

import cz.deuss.external.api.model.Currency
import cz.deuss.platform.offchain.framework.database.BaseEntityWithTimestamps
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import org.hibernate.annotations.JdbcType
import org.hibernate.dialect.PostgreSQLEnumJdbcType
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "payment")
class Payment(

    @Column(name = "deal_id", nullable = false)
    var dealId: BigInteger,

    // Hexadecimal onchain id of buyer's company
    @Column(name = "buyer_id", nullable = false)
    var buyerId: String,

    // Hexadecimal onchain id of seller's company
    @Column(name = "seller_id", nullable = false)
    var sellerId: String,

    @Column(name = "total_price", nullable = false, precision = 18, scale = 3)
    var totalPrice: BigDecimal,

    @Column(name = "currency", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcType(value = PostgreSQLEnumJdbcType::class)
    var currency: Currency,

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcType(value = PostgreSQLEnumJdbcType::class)
    var status: Status,

    @Column(name = "timeout", nullable = false)
    var timeout: LocalDateTime,

    // Time of creation on blockchain
    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime,

    @Column(name = "transaction_reference", nullable = false)
    var transactionReference: Long,

    @Column(name = "buyer_iban", nullable = false)
    var buyerIban: String,

    @Column(name = "seller_iban", nullable = false)
    var sellerIban: String,

    @Column(name = "buyer_company_id", nullable = false)
    var buyerCompanyId: UUID,

    @Column(name = "seller_company_id", nullable = false)
    var sellerCompanyId: UUID,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "payment_provider_id")
    var paymentProvider: PaymentProvider,

    ) : BaseEntityWithTimestamps() {

    enum class Status {
        NON_EXISTING,
        PENDING,
        PAID,
        NOT_PAID,
        CANCELLED,
        IN_DISPUTE,
        SETTLED,
    }
}
