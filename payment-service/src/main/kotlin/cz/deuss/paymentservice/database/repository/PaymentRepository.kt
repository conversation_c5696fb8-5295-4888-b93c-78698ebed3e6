package cz.deuss.paymentservice.database.repository

import cz.deuss.paymentservice.database.model.Payment
import io.micronaut.data.annotation.Query
import io.micronaut.data.annotation.Repository
import io.micronaut.data.jpa.repository.JpaRepository
import java.util.UUID

@Repository
interface PaymentRepository : JpaRepository<Payment, UUID> {
    @Query(value = """
        SELECT p FROM Payment p
        ORDER BY p.created DESC
        LIMIT 1 
    """)
    fun findNewestPayment(): Payment?

    fun findByDealId(dealId: Long): Payment?

    fun findByTransactionReference(transactionReference: Long): Payment?

    fun findByPaymentProviderId(paymentProviderId: UUID): List<Payment>

    fun existsByPaymentProviderId(paymentProviderId: UUID): Boolean
}
