package cz.deuss.paymentservice.database.repository

import cz.deuss.paymentservice.database.model.PaymentProvider
import io.micronaut.data.annotation.Repository
import io.micronaut.data.jpa.repository.JpaRepository
import java.util.UUID

@Repository
interface PaymentProviderRepository : JpaRepository<PaymentProvider, UUID> {

    fun findFirst(): PaymentProvider

    fun findByIdIn(ids: Collection<UUID>): List<PaymentProvider>

    fun findByNameContainingIgnoreCase(name: String): List<PaymentProvider>

}
