package cz.deuss.paymentservice.database.model

import cz.deuss.platform.offchain.framework.database.BaseEntityWithTimestamps
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table

@Entity
@Table(name = "payment_provider")
class PaymentProvider(

    @Column(name = "name", nullable = false)
    var name: String,

    @Column(name = "iban", nullable = false)
    var iban: String,

    @Column(name = "bic", nullable = false)
    var bic: String,

) : BaseEntityWithTimestamps()
