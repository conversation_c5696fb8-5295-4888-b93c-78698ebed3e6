package cz.deuss.paymentservice.client.paymentProvider

import cz.deuss.external.api.model.TransactionDto
import io.micronaut.http.HttpHeaders.AUTHORIZATION
import io.micronaut.http.HttpHeaders.USER_AGENT
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Headers
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.http.client.annotation.Client
import io.micronaut.serde.annotation.Serdeable
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull


@Client("\${payment-provider.host}")
@Headers(
    Header(name = USER_AGENT, value = "DEUSS blockchain"),
    Header(name = AUTHORIZATION, value = "Bearer \${payment-provider.bearer-token}"),
)
interface EbsiPaymentProviderClient {

    @Post("/create")
    fun createPayment(
        @Body @NotNull @Valid transactionDTO: TransactionDto,
    ): EbsiResponse?

    @Get("/status")
    fun getPaymentStatus(
        @QueryValue("reference") @NotNull reference: String,
    ): EbsiResponse?

    @Post("/validate")
    fun validatePayment(
        @Body @NotNull @Valid transactionDTO: TransactionDto,
    ): EbsiResponse?
}

@Serdeable.Deserializable
data class EbsiResponse(
    val success: Boolean,
    val payload: TransactionDto?,
)
