package cz.deuss.paymentservice.client.indexer

import com.apollographql.apollo.ApolloCall
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.api.CustomScalarAdapters
import com.apollographql.apollo.api.Query
import cz.deuss.indexer.client.type.BigInt
import cz.deuss.indexer.client.type.DateTime
import cz.deuss.paymentservice.apollo.BigIntAdapter
import cz.deuss.paymentservice.apollo.DateTimeAdapter
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton

@Singleton
class IndexerClient(
    @Value("\${micronaut.http.services.indexer.url}")
    private val indexerUrl: String
) {
    val customScalarAdapters = CustomScalarAdapters.Builder()
        .add(DateTime.type, DateTimeAdapter)
        .add(BigInt.type, BigIntAdapter)
        .build()

    private val apolloClient: ApolloClient = ApolloClient.Builder()
        .serverUrl(indexerUrl)
        .customScalarAdapters(customScalarAdapters)
        .build()

    fun <D : Query.Data> query(query: Query<D>): ApolloCall<D> {
        return apolloClient.query(query)
    }
}