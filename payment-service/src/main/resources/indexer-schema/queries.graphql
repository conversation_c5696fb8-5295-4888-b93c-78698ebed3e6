# Below you can find all queries used within the service.
# From all of them a<PERSON><PERSON> generates a client code which can be used in Kotlin.

query GetDealsSince($since: DateTime) {
    marketDeals(
        where: {createdAt_gt: $since}
        orderBy: createdAt_ASC
    ) {
        id
        dealId
        createdBy
        marketOffer {
            offerId
            owner
            currency
        }
        amount
        price
        expiry
        disputeBuffer
        status
        createdAt
        updatedAt
    }
}