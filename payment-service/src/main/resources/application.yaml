micronaut:
  application:
    name: payment-service
  server:
    port: 8080
    netty:
      access-logger:
        enabled: true
  http:
    services:
      indexer:
        url: ${INDEXER_URL:`http://147.175.151.62:8350/graphql`}
  security:
    token:
      propagation:
        header:
          enabled: true
          header-name: "Authorization"
        enabled: true
        service-id-regex: "payment-service"

jpa:
  default:
    entity-scan:
      packages: 'cz.deuss.paymentservice.database.model'

# Default argon2 config. Whenever changed, stored passwords need to be regenerated.
argon2-configuration:
  iterations: 10
  memory: 65536
  parallelism: 1

flyway:
  datasources:
    default:
      enabled: true
      locations: db/migration

datasources:
  default:
    db-type: postgres
    dialect: POSTGRES
    driver-class-name: org.postgresql.Driver
    url: ${DATABASE_URL:`***************************************************`}
    username: ${DATABASE_USER:payment_service_user}
    password: ${DATABASE_PASSWORD:payment_service}

payment-provider:
  # Address and token to reach PP
  # get here https://vaultwarden.deussblockchain.eu/#/vault?organizationId=8538c467-0705-4dfc-8572-a68e24b1bb12&itemId=49c70255-a774-4cc3-97d0-c185a4b6f9b2
  bearer-token: ${PAYMENT_PROVIDER_TOKEN}
  host: ${PAYMENT_PROVIDER_HOST:`https://ebsi-dev.bonesystems.cz/api/payment`}
  # For PP to access our services
  credentials:
    username: ${PAYMENT_PROVIDER_USERNAME:}
    password-hash: ${PAYMENT_PROVIDER_PASSWORD_HASH} # Argon2 hash of password. We don't store the original.

deuss:
  blockchain:
    url: ${CHAIN_URL}
    contracts:
      company-wallet-registry: ${CHAIN_COMPANY_WALLET_REGISTRY_ADDRESS}
      bond-registry-v2: ${CHAIN_BOND_REGISTRY_V2_ADDRESS}
      admin-privateKey: ${CHAIN_ADMIN_PRIVATE_KEY}