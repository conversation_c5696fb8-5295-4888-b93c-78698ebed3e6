openapi: 3.0.0
info:
  title: EBSI payment api
  version: 0.0.1
servers:
  - url: http://ebsi-dev.bonesystems.cz/api/payment
    description: Payments test service for EBSI payments api
paths:
  /validate:
    post:
      tags:
        - transaction-controller
      operationId: validatePayment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: object
  /create:
    post:
      tags:
        - transaction-controller
      operationId: createPayment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: object
  /status:
    get:
      tags:
        - transaction-controller
      operationId: getPaymentStatus
      parameters:
        - name: reference
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: object
components:
  schemas:
    TransactionDto:
      type: object
      properties:
        amount:
          type: number
          format: double
        currency:
          $ref: "../../../../../api-specifications/src/main/resources/common/common_constructs_openapi.yaml#/components/schemas/Currency"
        sender_iban:
          type: string
        sender_bic:
          type: string
        receiver_iban:
          type: string
        receiver_bic:
          type: string
        transaction_reference:
          type: string
      required:
        - amount
        - currency
        - receiver_iban
        - sender_iban
        - transaction_reference