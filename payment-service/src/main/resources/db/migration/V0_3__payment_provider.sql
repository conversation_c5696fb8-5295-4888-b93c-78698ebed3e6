
CREATE TABLE if not exists payment_provider
(
    id          UUID                             PRIMARY KEY,
    name        varchar(256)                     NOT NULL,
    iban        varchar(256)                     NOT NULL,
    bic         varchar(256)                     NOT NULL,
    created     TIMESTAMP      DEFAULT NOW()     NOT NULL,
    last_edit   TIMESTAMP      DEFAULT NOW()     NOT NULL
);

insert into payment_provider VALUES ('983dc506-256b-4c31-87f4-70607a48c82c', 'EBSI Payment provider', '*********************', 'EBSI', NOW(), NOW());

ALTER TABLE payment ADD COLUMN payment_provider_id UUID;
alter table payment add constraint pp_fk foreign key (payment_provider_id) references payment_provider;

UPDATE payment SET payment_provider_id = '983dc506-256b-4c31-87f4-70607a48c82c';

ALTER TABLE payment ALTER COLUMN payment_provider_id SET NOT NULL;
