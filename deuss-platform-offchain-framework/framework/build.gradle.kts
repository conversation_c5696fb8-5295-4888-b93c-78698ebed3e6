plugins {
    id(libs.plugins.kotlin.noarg.get().pluginId)
    id(libs.plugins.kotlin.allOpen.get().pluginId)
    id(libs.plugins.google.ksp.get().pluginId)
    id(libs.plugins.micronaut.openapi.get().pluginId)
    id(libs.plugins.kotlin.jvm.get().pluginId)
}

dependencies {
    implementation(platform(libs.micronaut.platform))
    implementation(libs.bundles.micronaut.validation)
    implementation("io.micronaut:micronaut-http")
    implementation("io.micronaut:micronaut-http-server")
    implementation("jakarta.validation:jakarta.validation-api")
    implementation("io.micronaut.serde:micronaut-serde-jackson")
    implementation("io.micronaut.data:micronaut-data-hibernate-jpa")
    implementation("io.micronaut.validation:micronaut-validation")
    implementation("io.micronaut.security:micronaut-security")
    runtimeOnly("org.yaml:snakeyaml")
    implementation(libs.bundles.kotlin)
    implementation(libs.bundles.logging)
    testImplementation(libs.bundles.test.base)
    testImplementation(platform(libs.test.containers.bom))
    testImplementation(libs.bundles.test.containers)
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testImplementation(libs.io.mockk)
    testImplementation(libs.bundles.test.kotest)
    testImplementation(libs.mockwebserver)
}

micronaut {
    version = libs.versions.micronaut.version.get()
    testRuntime("junit5")

    openapi {
        server(file("src/main/resources/common_constructs_openapi.yaml")) {
            modelPackageName = "cz.deuss.platform.offchain.framework.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
        }
    }
}


tasks {
    test {
        testLogging {
            showStandardStreams = true
            showStackTraces = true
            showCauses = true
            showExceptions = true
            events("passed", "skipped", "failed")
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        }
        useJUnitPlatform {
            includeEngines("junit-jupiter", "kotest")
        }
        reports {
            junitXml.outputLocation = file("${project.layout.buildDirectory.get()}/reports/tests/test/xml")
        }
    }
}
