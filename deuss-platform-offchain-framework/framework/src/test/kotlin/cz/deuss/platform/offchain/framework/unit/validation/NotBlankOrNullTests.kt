package cz.deuss.platform.offchain.framework.unit.validation

import cz.deuss.platform.offchain.framework.validation.NotBlankOrNullValidator
import io.mockk.mockk
import jakarta.validation.ConstraintValidatorContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource


class NotBlankOrNullTests {
    private val validator = NotBlankOrNullValidator()
    private val context: ConstraintValidatorContext = mockk<ConstraintValidatorContext>()

    @Test
    fun `constraint is not violated when string is null`() {
        val isValid = validator.isValid(null, context)

        assertThat(isValid).isTrue()
    }

    @ParameterizedTest
    @ValueSource(strings = ["DEUSS is the next BitCoin!!!", " Banks hate us!    ", "         <PERSON><PERSON><PERSON><PERSON><PERSON>        "])
    fun `constraint is not violated when string is not blank`(payload: String) {
        val isValid = validator.isValid(payload, context)

        assertThat(isValid).isTrue()
    }


    @ParameterizedTest
    @ValueSource(strings = ["   ", " ", ""])
    fun `constraint is violated when string is blank`(payload: String) {
        val isValid = validator.isValid(payload, context)

        assertThat(isValid).isFalse()
    }


}