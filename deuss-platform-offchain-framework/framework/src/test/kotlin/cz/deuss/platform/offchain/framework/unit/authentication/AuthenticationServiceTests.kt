package cz.deuss.platform.offchain.framework.unit.authentication

import com.fasterxml.jackson.databind.ObjectMapper
import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.authentication.AuthenticationService
import cz.deuss.platform.offchain.framework.authentication.Role
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.utils.SecurityService
import io.mockk.every
import io.mockk.mockk
import java.util.Optional
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class AuthenticationServiceTests {

    private val securityService = mockk<SecurityService>()
    private val tested = AuthenticationService(securityService, ObjectMapper())

    @Test
    fun `extract authentication`() {
        val email = "email@email"
        val roles = Role.Enum.entries
        val userId = AuthenticatedUserId(UUID.randomUUID())

        val attributes = mapOf<String, Any>(
            AuthenticationService.Attributes.USER_ID_KEY to userId.id.toString(),
            AuthenticationService.Attributes.EMAIL_KEY to email
        )

        val authentication = Authentication.build(email, roles.map { it.roleName }, attributes)
        every { securityService.authentication } returns Optional.of(authentication)
        val extracted = tested.extractAuthentication()
        assertThat(extracted.userId.id).isEqualTo(userId.id)
        assertThat(extracted.email).isEqualTo(email)
    }
}
