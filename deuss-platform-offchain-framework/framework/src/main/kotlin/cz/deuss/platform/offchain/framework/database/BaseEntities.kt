package cz.deuss.platform.offchain.framework.database

import io.micronaut.core.annotation.Introspected
import jakarta.persistence.Id
import jakarta.persistence.MappedSuperclass
import java.time.LocalDateTime
import java.util.UUID
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.hibernate.proxy.HibernateProxy

@MappedSuperclass
@Introspected
abstract class BaseEntity(
    @Id
    val id: UUID = UUID.randomUUID()
) {


    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null) return false
        val oEffectiveClass =
            if (other is HibernateProxy) other.hibernateLazyInitializer.persistentClass else other.javaClass
        val thisEffectiveClass =
            if (this is HibernateProxy) this.hibernateLazyInitializer.persistentClass else this.javaClass
        if (thisEffectiveClass != oEffectiveClass) return false
        other as BaseEntity

        return id == other.id
    }

    override fun hashCode(): Int =
        if (this is HibernateProxy) this.hibernateLazyInitializer.persistentClass.hashCode() else javaClass.hashCode()
}

@MappedSuperclass
abstract class BaseEntityWithTimestamps(id: UUID = UUID.randomUUID()) : BaseEntity(id = id) {

    @CreationTimestamp
    val created: LocalDateTime = LocalDateTime.now()

    @UpdateTimestamp
    val lastEdit: LocalDateTime = LocalDateTime.now()
}