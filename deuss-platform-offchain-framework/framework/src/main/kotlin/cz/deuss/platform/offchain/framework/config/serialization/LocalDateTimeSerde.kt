package cz.deuss.platform.offchain.framework.config.serialization

import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import io.micronaut.context.annotation.Primary
import io.micronaut.core.type.Argument
import io.micronaut.serde.Decoder
import io.micronaut.serde.Deserializer
import io.micronaut.serde.Encoder
import io.micronaut.serde.Serde
import io.micronaut.serde.Serializer
import jakarta.inject.Singleton
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException


/**
 * This beans overrides micronaut bean which serializes/deserializes date times from FE. I have tried to use date-format property, but it is used for datetime as well as for date, and it does not work properly, because date doesn't have time...
 */
@Singleton
@Primary
class LocalDateTimeSerde : Serde<LocalDateTime> {
    private val formatter = DateTimeFormatter.ofPattern(PATTERN)
    override fun serialize(
        encoder: Encoder,
        context: Serializer.EncoderContext,
        type: Argument<out LocalDateTime>,
        value: LocalDateTime,
    ) {
        encoder.encodeString(formatter.format(value))
    }

    override fun deserialize(
        decoder: Decoder,
        context: Deserializer.DecoderContext,
        type: Argument<in LocalDateTime>,
    ): LocalDateTime {
        decoder.decodeString().let {
            try {
                return LocalDateTime.parse(it, formatter)
            } catch (e: DateTimeParseException) {
                throw BadRequestException("Invalid format of date, please use $PATTERN")
            }
        }
    }
    private companion object{
        private const val PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
    }
}
