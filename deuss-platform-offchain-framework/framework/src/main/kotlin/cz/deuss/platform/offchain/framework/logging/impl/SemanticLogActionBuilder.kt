package cz.deuss.platform.offchain.framework.logging.impl

import cz.deuss.platform.offchain.framework.logging.FiningLoggingStep
import cz.deuss.platform.offchain.framework.logging.LogActionResultStep
import cz.deuss.platform.offchain.framework.logging.LogAtLevel
import cz.deuss.platform.offchain.framework.logging.LogParamStep
import cz.deuss.platform.offchain.framework.logging.LogThrowable
import io.github.oshai.kotlinlogging.KLogger

internal class SemanticLogActionBuilder(
    actionId: String,
    logger: KLogger,
    private val collector: ParamAndThrowableLogCollector = ParamAndThrowableLogCollector()
) : FiningLoggingStep, LogActionResultStep, LogParamStep by collector, LogThrowable by collector, LogAtLevel by LogAtLevelImpl(collector, logger) {

    init {
        collector.typedThis = { this }
        collector.param("actionId", actionId)
    }

    override fun started(): FiningLoggingStep {
        collector.param("actionType", "STARTED")

        return this
    }

    override fun failed(): FiningLoggingStep {
        collector.param("actionType", "FAILED")

        return this
    }

    override fun finished(): FiningLoggingStep {
        collector.param("actionType", "FINISHED")

        return this
    }
}
