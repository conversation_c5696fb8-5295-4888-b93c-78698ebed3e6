package cz.deuss.platform.offchain.framework.authentication

/**
 * This class serves for annotation security and it is tightly coupled with inner class [Role.Enum], so if you are adding constant to [Role] please add it also to [Role.Enum].
 */
data object Role {
    const val USER = "ROLE_USER"
    const val ADMIN = "ROLE_ADMIN"
    const val ORIGINATOR = "ROLE_ORIGINATOR"

    enum class Enum(val roleName: String) {
        USER(Role.USER),
        ADMIN(Role.ADMIN),
        ORIGINATOR(Role.ORIGINATOR),
        ;
    }
}
