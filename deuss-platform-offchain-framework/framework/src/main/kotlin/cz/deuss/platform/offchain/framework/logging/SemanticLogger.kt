package cz.deuss.platform.offchain.framework.logging

import cz.deuss.platform.offchain.framework.logging.impl.SemanticLogActionBuilder
import cz.deuss.platform.offchain.framework.logging.impl.SemanticLogMessageBuilder
import io.github.oshai.kotlinlogging.KLogger
import java.util.UUID

class SemanticLogger(
    private val logger: <PERSON>Logger,
) : KLogger by logger {

    /**
     * Action represent event, which should be part of log.
     * Action usually start and finish with either success or failure. So reader of logs can tell how action unfolded.
     *
     * @param actionId should be unique across the service - easier search.
     */
    fun action(actionId: String): LogActionResultStep {
        return SemanticLogActionBuilder(actionId, logger)
    }

    /**
     * Use message, when event does not have start and end. It is only one occurrence.
     */
    fun message(message: String): FiningLoggingStep {
        return SemanticLogMessageBuilder(message, logger)
    }
}

interface LogParamStep {
    fun param(name: String, value: String?): FiningLoggingStep
    fun param(name: String, value: Int): FiningLoggingStep
    fun param(name: String, value: Long): FiningLoggingStep
    fun param(name: String, value: UUID): FiningLoggingStep
    fun param(name: String, value: Boolean?): FiningLoggingStep
}

interface LogThrowable {
    fun throwable(e: Throwable?): FiningLoggingStep
}

interface LogActionResultStep {
    fun started(): FiningLoggingStep
    fun failed(): FiningLoggingStep
    fun finished(): FiningLoggingStep
}

interface FiningLoggingStep : LogParamStep, LogThrowable, LogAtLevel

/**
 * Log level used for produced log.
 */
interface LogAtLevel {
    fun debug()
    fun info()
    fun warn()
    fun error()
}
