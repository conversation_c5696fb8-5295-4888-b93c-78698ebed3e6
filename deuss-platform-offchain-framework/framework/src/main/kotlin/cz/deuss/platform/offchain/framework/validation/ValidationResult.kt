package cz.deuss.platform.offchain.framework.validation

sealed interface ValidationResult<ERROR, VALID> {

    fun validOrThrow(exception: (ERROR) -> Exception): VALID
}

data class Valid<ERROR, VALID>(
    val data: VALID,
): ValidationResult<ERROR, VALID> {
    override fun validOrThrow(exception: (ERROR) -> Exception): VALID {
        return data
    }
}

data class Invalid<ERROR, VALID>(
    val error: ERROR,
): ValidationResult<ERROR, VALID> {
    override fun validOrThrow(exception: (ERROR) -> Exception): <PERSON><PERSON><PERSON> { throw exception(error) }
}
