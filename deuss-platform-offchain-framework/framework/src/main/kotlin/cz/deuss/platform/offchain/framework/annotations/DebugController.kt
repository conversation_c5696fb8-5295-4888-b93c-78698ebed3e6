package cz.deuss.platform.offchain.framework.annotations

import cz.deuss.platform.offchain.framework.annotations.DebugController.Companion.API_PREFIX
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.DefaultScope
import io.micronaut.context.annotation.Requires
import io.micronaut.http.annotation.Controller
import jakarta.inject.Singleton

@Requires(env = ["local", "dev"])
@MustBeDocumented
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.ANNOTATION_CLASS, AnnotationTarget.CLASS)
@Bean
@DefaultScope(Singleton::class)
@Controller(API_PREFIX)
annotation class DebugController {
    companion object {
        const val API_PREFIX = "/api/debug"
    }
}
