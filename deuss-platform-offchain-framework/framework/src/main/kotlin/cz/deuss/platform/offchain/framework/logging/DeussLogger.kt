package cz.deuss.platform.offchain.framework.logging

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlin.reflect.KClass

object DeussLogger {

    fun semanticLogger(clazz: KClass<*>): SemanticLogger {
        return semanticLoggerForClass(clazz)
    }

    /**
     * Audit logging can be differentiated with current logging framework only by logger name.
     * Kotlin logging does not support custom log level.
     */
    fun auditLogger(): SemanticLogger {
        return semanticLoggerForClass(DeussLogger::class)
    }

    private fun semanticLoggerForClass(clazz: KClass<*>): SemanticLogger {
        val javaClass = clazz.java
        val declaringClass = javaClass.declaringClass
        val loggerName = declaringClass?.name ?: javaClass.name

        return SemanticLogger(KotlinLogging.logger(loggerName))
    }
}

