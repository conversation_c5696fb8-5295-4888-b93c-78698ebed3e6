package cz.deuss.platform.offchain.framework.logging.impl

import cz.deuss.platform.offchain.framework.logging.LogAtLevel
import io.github.oshai.kotlinlogging.KLogger
import io.github.oshai.kotlinlogging.Level

internal class LogAtLevelImpl(
    private val collected: ParamAndThrowableLogCollector,
    private val logger: KLogger,
) : LogAtLevel {

    override fun debug() {
        logAt(Level.DEBUG)
    }

    override fun info() {
        logAt(Level.INFO)
    }

    override fun error() {
        logAt(Level.ERROR)
    }

    override fun warn() {
        logAt(Level.WARN)
    }

    private fun logAt(level: Level) {
        logger.at(level) {
            cause = collected.throwable
            payload = collected.params
        }
    }
}
