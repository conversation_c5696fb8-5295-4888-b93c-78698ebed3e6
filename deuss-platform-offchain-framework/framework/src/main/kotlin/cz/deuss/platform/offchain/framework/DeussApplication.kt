package cz.deuss.platform.offchain.framework

import io.micronaut.context.env.PropertySource
import io.micronaut.runtime.Micronaut
import org.yaml.snakeyaml.Yaml

fun runDeussApp(args: Array<String>) {
    val inputStream = Unit::class.java.classLoader.getResourceAsStream("application-framework.yaml")

    val yamlMap: Map<String, Any> = Yaml().load(inputStream)
    val customPropertySource = PropertySource.of("application-framework.yaml", yamlMap, -400)

    Micronaut.build(*args)
        .eagerInitConfiguration(true)
        .eagerInitSingletons(true)
        .propertySources(customPropertySource)
        .start()
}