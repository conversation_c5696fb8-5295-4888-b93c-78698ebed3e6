package cz.deuss.platform.offchain.framework.authentication

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import io.micronaut.core.annotation.Introspected
import io.micronaut.security.utils.SecurityService
import jakarta.inject.Singleton
import java.util.UUID

@Singleton
class AuthenticationService(
    private val securityService: SecurityService,
    private val objectMapper: ObjectMapper,
) {

    /**
     * Extract [SecurityService.getAuthentication] to known format.
     * If user is not authenticated, then null is returned. Otherwise Authentication data filled with user data from JWT are returned
     */
    fun extractAuthentication(): AuthenticationData = securityService.authentication.map {
        val attributes = objectMapper.convertValue(it.attributes, Attributes::class.java)
        AuthenticationData(
            userId = AuthenticatedUserId(attributes.userId),
            email = attributes.email,
            roles = it.roles.map { role -> Role.Enum.valueOf( role.removePrefix("ROLE_")) }.toSet()
        )
    }.orElseThrow { IllegalStateException("User is not authenticated!") }


    @Introspected
    data class Attributes(
        @JsonProperty(USER_ID_KEY)
        val userId: UUID,
        @JsonProperty(EMAIL_KEY)
        val email: String,
    ) {
        companion object {
            const val USER_ID_KEY = "userId"
            const val EMAIL_KEY = "email"
        }
    }

    data class AuthenticationData(
        val userId: AuthenticatedUserId,
        val email: String,
        val roles: Set<Role.Enum>,
    )
}


@JvmInline
value class AuthenticatedUserId(val id: UUID)
