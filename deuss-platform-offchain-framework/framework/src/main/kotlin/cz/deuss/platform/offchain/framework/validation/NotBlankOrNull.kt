package cz.deuss.platform.offchain.framework.validation

import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass


@Target(
    AnnotationTarget.FIELD,
    AnnotationTarget.PROPERTY_GETTER,
    AnnotationTarget.VALUE_PARAMETER,
    AnnotationTarget.PROPERTY
)
@Constraint(validatedBy = [NotBlankOrNullValidator::class])
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
annotation class NotBlankOrNull(
    val message: String = "when not null, it must be not blank",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = []
)


class NotBlankOrNullValidator : ConstraintValidator<NotBlankOrNull, String?> {
    override fun isValid(value: String?, context: ConstraintValidatorContext): Boolean {
        if (value === null) {
            return true
        }
        return value.isNotBlank()
    }
}
