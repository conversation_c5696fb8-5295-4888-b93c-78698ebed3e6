package cz.deuss.platform.offchain.framework.authentication

import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory

@Factory
class AuthenticationContextFactory(
    private val authenticationService: AuthenticationService,
) {

    @Bean
    fun authenticatedUserId(): () -> AuthenticatedUserId {
        return { authenticationService.extractAuthentication().userId }
    }

    @Bean
    fun authenticationUserData(): () -> AuthenticationService.AuthenticationData {
        return { authenticationService.extractAuthentication() }
    }
}