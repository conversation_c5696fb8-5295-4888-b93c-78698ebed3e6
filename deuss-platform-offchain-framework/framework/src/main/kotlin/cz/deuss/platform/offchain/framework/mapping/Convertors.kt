package cz.deuss.platform.offchain.framework.mapping

import cz.deuss.platform.offchain.framework.exceptions.InternalServerErrorException
import java.util.Optional

inline fun <reified IN : Enum<IN>, reified OUT : Enum<OUT>> IN.convertTo(errorLog: () -> Unit): Optional<OUT> {
    val enumValue = enumValues<OUT>().firstOrNull { this.name == it.name }
    if (enumValue == null) {
        errorLog.invoke()
        throw InternalServerErrorException("Error occurred during mapping of types")
    }
    return Optional.of(enumValue)
}