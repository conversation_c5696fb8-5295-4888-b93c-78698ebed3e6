package cz.deuss.platform.offchain.framework.logging.impl

import cz.deuss.platform.offchain.framework.logging.FiningLoggingStep
import cz.deuss.platform.offchain.framework.logging.LogParamStep
import cz.deuss.platform.offchain.framework.logging.LogThrowable
import java.util.UUID

internal class ParamAndThrowableLogCollector : LogParamStep, LogThrowable {
    var throwable: Throwable? = null
    val params: MutableMap<String, Any?> = mutableMapOf()
    lateinit var typedThis: () -> FiningLoggingStep

    override fun param(name: String, value: String?): FiningLoggingStep {
        params[name] = value
        return typedThis()
    }

    override fun param(name: String, value: Int): FiningLoggingStep {
        params[name] = value
        return typedThis()
    }

    override fun param(name: String, value: UUID): FiningLoggingStep {
        params[name] = value.toString()
        return typedThis()
    }

    override fun param(name: String, value: Long): FiningLoggingStep {
        params[name] = value
        return typedThis()
    }

    override fun param(name: String, value: Boolean?): FiningLoggingStep {
        params[name] = value
        return typedThis()
    }

    override fun throwable(e: Throwable?): FiningLoggingStep {
        throwable = e

        return typedThis()
    }
}

