package cz.deuss.platform.offchain.framework.annotations

import cz.deuss.platform.offchain.framework.annotations.ExposedController.Companion.API_PREFIX
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.DefaultScope
import io.micronaut.http.annotation.Controller
import jakarta.inject.Singleton

@MustBeDocumented
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.ANNOTATION_CLASS, AnnotationTarget.CLASS)
@Bean
@DefaultScope(Singleton::class)
@Controller(API_PREFIX)
annotation class ExposedController {
    companion object {
        const val API_PREFIX = "/api/exposed"
    }
}
