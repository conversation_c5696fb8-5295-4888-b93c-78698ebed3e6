package cz.deuss.platform.offchain.framework.exceptions

import cz.deuss.platform.offchain.framework.api.model.HttpError
import cz.deuss.platform.offchain.framework.api.model.MessageDetail
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.core.convert.exceptions.ConversionErrorException
import io.micronaut.http.server.exceptions.ExceptionHandler
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Produces
import io.micronaut.http.server.exceptions.ErrorExceptionHandler
import jakarta.inject.Singleton
import jakarta.validation.ConstraintViolationException
import java.net.URI
import java.time.LocalDateTime

/**
 * A custom exception handler for handling exceptions of type [BaseHttpException] and all of its subclasses such as [NotFoundException].
 * This handler converts the exception into a standardized [HttpResponse] containing an [HttpError] defined in [openapi specification](src/main/resources/common_constructs_openapi.yaml) in [RFC 9457](https://www.rfc-editor.org/rfc/rfc9457.html) format.
 * <AUTHOR> Štengl (stengjir)
 */

@Produces
@Singleton
@Requires(classes = [BaseHttpException::class, ExceptionHandler::class])
class CustomExceptionHandler : ExceptionHandler<BaseHttpException, HttpResponse<HttpError>> {

    override fun handle(request: HttpRequest<*>, exception: BaseHttpException): HttpResponse<HttpError> =
        HttpResponse.status<Error?>(exception.status).body(exception.toError(request.uri))

}


/**
 * Global exception handler that overrides the default exception handler to catch unexpected exceptions. This handler is last in
 * exception handlers chain to catch all exception which occurred and were not caught by some other handlers. It always returns Internal Server Error, because
 * expected exception should not get here. Additional error details are logged using the KotlinLogging framework.
 *
 * It overrides [ErrorExceptionHandler], because it does not return errors in [RFC 9457](https://www.rfc-editor.org/rfc/rfc9457.html) format.
 *
 *
 *
 * This handler is marked as [Primary], ensuring it takes precedence over other exception handlers.
 * It is intended to handle any unanticipated exceptions and return a standardized
 * [HttpResponse] with an [HttpError] payload.
 *
 * The class is integrated with the Micronaut framework through annotations such as:
 * - [Singleton] to declare a single instance.
 * - [Requires] to load this handler only when [Exception] and [ExceptionHandler] classes are present.
 * - [Produces] to specify it as a provider.
 * - [Replaces] to override the default [ErrorExceptionHandler].
 * <AUTHOR> Štengl (stengjir)
 */

@Singleton
@Requires(classes = [Exception::class, ExceptionHandler::class])
@Primary
@Produces
@Replaces(ErrorExceptionHandler::class)
class GlobalExceptionHandler : ExceptionHandler<Throwable, HttpResponse<HttpError>> {
    private val logger = KotlinLogging.logger {}

    override fun handle(request: HttpRequest<*>, exception: Throwable): HttpResponse<HttpError> {
        logger.error(exception) { "Unexpected exception has occurred." }
        return HttpResponse.status<HttpError>(HttpStatus.INTERNAL_SERVER_ERROR).body(
            HttpError(
                type = TYPE,
                title = "Internal Server Error",
                status = HttpStatus.INTERNAL_SERVER_ERROR.code,
                detail = "Internal Server Error",
                timestamp = LocalDateTime.now(),
                messageDetails = emptyList(),
                instance = request.uri
            )
        )
    }

    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/server-error")
    }
}


/**
 * Exception handler for handling [ConstraintViolationException], which is thrown by Jakarta bean validation, and converting it to
 * an [RFC 9457](https://www.rfc-editor.org/rfc/rfc9457.html)-compliant [HttpError] response with HTTP status 400 (Bad Request).
 *
 * This handler is marked as [Primary], ensuring it takes precedence over other constraint
 * violation handlers. It replaces the default [ConstraintExceptionHandler], because it returned error in different format.
 *
 * When a constraint violation occurs (e.g., invalid method parameters), this handler
 * generates a standardized error response that adheres to RFC 9457 error response format.
 *
 * The response contains detailed information, including the error type, status code,
 * and a URI pointing to additional error details.
 *
 * <AUTHOR> Štengl (stengjir)
 */

@Singleton
@Requires(classes = [ConstraintViolationException::class, ExceptionHandler::class])
@Primary
@Replaces(ConstraintExceptionHandler::class)
class ConstraintExceptionHandler : ExceptionHandler<ConstraintViolationException, HttpResponse<HttpError>> {
    override fun handle(request: HttpRequest<*>, exception: ConstraintViolationException): HttpResponse<HttpError> {
        return HttpResponse.status<HttpError>(HttpStatus.BAD_REQUEST).body(
            HttpError(
                type = TYPE,
                title = "Bad Request",
                status = HttpStatus.BAD_REQUEST.code,
                detail = exception.message ?: "Validation error",
                timestamp = LocalDateTime.now(),
                messageDetails = emptyList(),
                instance = request.uri
            )
        )
    }

    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/bad-request")
    }
}

/**
 * Exception handler for [ConversionErrorException], which is thrown by Micronaut's deserialization layer
 * (serde or Jackson) when the incoming JSON cannot be converted into a Java/Kotlin object.
 *
 * This handler returns an [RFC 9457](https://www.rfc-editor.org/rfc/rfc9457.html)-compliant [HttpError]
 * response with HTTP status 400 (Bad Request).
 *
 * It is specifically triggered when the JSON structure is invalid or contains values that cannot be coerced
 * into the expected types.
 *
 * The response includes details about the invalid structure and the specific attribute (if available)
 * that caused the failure. A structured log entry is also emitted to Std Out, containing diagnostic
 * information for observability.
 *
 * Note: This handler does not cover constraint violations (e.g., field annotated with `@NotBlank`),
 * which are handled separately by a [ConstraintExceptionHandler] handler.
 *
 * <AUTHOR> Štengl (stengjir)
 */

@Produces
@Singleton
@Requires(classes = [ConversionErrorException::class, ExceptionHandler::class])
class ConversionErrorExceptionHandler : ExceptionHandler<ConversionErrorException, HttpResponse<HttpError>> {

    private val logger = KotlinLogging.logger {}

    override fun handle(request: HttpRequest<*>, exception: ConversionErrorException): HttpResponse<HttpError> {
        logger.info { "Deserialization error during request: ${request}; Error during deserialization of type: ${exception.argument.type}; Caused by: ${exception.conversionError.cause.cause};" }

        return HttpResponse.status<HttpError>(HttpStatus.BAD_REQUEST).body(
            HttpError(
                type = TYPE,
                title = "Bad Request",
                status = HttpStatus.BAD_REQUEST.code,
                detail = "Deserialization error due to invalid JSON structure.",
                timestamp = LocalDateTime.now(),
                messageDetails = listOf(MessageDetail("invalidArgumentName", listOf(exception.argument.name))),
                instance = request.uri
            )
        )
    }

    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/bad-request")
    }
}

