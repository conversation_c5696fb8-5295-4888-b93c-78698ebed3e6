package cz.deuss.platform.offchain.framework.exceptions


import io.micronaut.http.HttpStatus
import java.net.URI
import java.time.LocalDateTime
import cz.deuss.platform.offchain.framework.api.model.HttpError
import cz.deuss.platform.offchain.framework.api.model.MessageDetail

abstract class BaseHttpException(
    message: String,
    private val type: URI,
    val status: HttpStatus,
    private val messageDetails: Map<String, Collection<String>>
) : RuntimeException(message) {
    fun toError(instance: URI? = null): HttpError = HttpError(
        title = status.reason,
        type = type,
        status = status.code,
        timestamp = LocalDateTime.now(),
        instance = instance,
        messageDetails = messageDetails.convertToMessageDetails(),
        detail = message ?: "Unknown error"
    )


    private fun Map<String, Collection<String>>.convertToMessageDetails(): List<MessageDetail> = this.map { (k, v) ->
        MessageDetail(k, v.toList())
    }
}

class NotFoundException(
    message: String,
    messageDetails: Map<String, Collection<String>> = emptyMap()
) : BaseHttpException(
    message,
    type = TYPE,
    status = HttpStatus.NOT_FOUND,
    messageDetails = messageDetails
) {
    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/not-found")
    }

}

class ConflictException(
    message: String,
    messageDetails: Map<String, Collection<String>> = emptyMap()
) : BaseHttpException(
    message,
    type = TYPE,
    status = HttpStatus.CONFLICT,
    messageDetails = messageDetails
) {
    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/conflict")
    }
}


class BadRequestException(
    message: String,
    messageDetails: Map<String, Collection<String>> = emptyMap()
) : BaseHttpException(
    message,
    type = TYPE,
    status = HttpStatus.BAD_REQUEST,
    messageDetails = messageDetails
) {
    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/bad-request")
    }
}

class InternalServerErrorException(
    message: String,
    messageDetails: Map<String, Collection<String>> = emptyMap()
) : BaseHttpException(
    message,
    type = TYPE,
    status = HttpStatus.INTERNAL_SERVER_ERROR,
    messageDetails = messageDetails
) {
    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/server-error")
    }
}

class ForbiddenException(
    message: String,
    messageDetails: Map<String, Collection<String>> = emptyMap()
) : BaseHttpException(
    message,
    type = TYPE,
    status = HttpStatus.FORBIDDEN,
    messageDetails = messageDetails
) {
    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/forbidden")
    }
}

class UnauthorizedException(
    message: String,
    messageDetails: Map<String, Collection<String>> = emptyMap()
) : BaseHttpException(
    message,
    type = TYPE,
    status = HttpStatus.UNAUTHORIZED,
    messageDetails = messageDetails
) {
    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/unauthorized")
    }
}


class ServiceUnavailableException(
    message: String,
    messageDetails: Map<String, Collection<String>> = emptyMap()
) : BaseHttpException(
    message,
    type = TYPE,
    status = HttpStatus.SERVICE_UNAVAILABLE,
    messageDetails = messageDetails
) {
    companion object {
        private val TYPE = URI.create("https://problems-registry.smartbear.com/service-unavailable/")
    }
}
