package cz.deuss.platform.offchain.framework.logging.impl

import cz.deuss.platform.offchain.framework.logging.FiningLoggingStep
import cz.deuss.platform.offchain.framework.logging.LogAtLevel
import cz.deuss.platform.offchain.framework.logging.LogParamStep
import cz.deuss.platform.offchain.framework.logging.LogThrowable
import io.github.oshai.kotlinlogging.KLogger

internal class SemanticLogMessageBuilder(
    message: String,
    logger: KLogger,
    private val collector: ParamAndThrowableLogCollector = ParamAndThrowableLogCollector(),
) : FiningLoggingStep, LogParamStep by collector, LogThrowable by collector, LogAtLevel by LogAtLevelImpl(collector, logger) {

    init {
        collector.typedThis = { this }
        collector.param("message", message)
    }
}
