plugins {
    id(libs.plugins.kotlin.jvm.get().pluginId)
}

dependencies {
    implementation(platform(libs.micronaut.platform))
    implementation(libs.bundles.kotlin)
    implementation(libs.bundles.test.base)
    implementation(libs.bundles.test.containers)
    implementation(libs.bundles.micronaut.security)
    implementation(libs.io.mockk)
    implementation("io.micronaut:micronaut-http-client")
    implementation(project(":deuss-platform-offchain-framework:framework"))
    implementation("io.micronaut.test:micronaut-test-junit5")
    implementation("io.micronaut:micronaut-jackson-databind")
}
