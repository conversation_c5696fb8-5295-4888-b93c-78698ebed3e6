package cz.deuss.platform.offchain.test.bean

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import cz.deuss.platform.offchain.framework.authentication.AuthenticationService
import cz.deuss.platform.offchain.framework.authentication.Role
import io.micronaut.context.annotation.Bean
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.token.generator.AccessRefreshTokenGenerator
import java.util.UUID


@Bean
internal class TokenGenerator(
    private val tokenGenerator: AccessRefreshTokenGenerator,
    private val objectMapper: ObjectMapper,
) {

    fun generateToken(
        userId: UUID,
        email: String,
        roles: Set<Role.Enum> = setOf(Role.Enum.USER),
    ): String {
        val attributes = AuthenticationService.Attributes(
            userId = userId,
            email = email,
        )
        val serializedAttributes = objectMapper.convertValue(attributes, object : TypeReference<Map<String, Any>>() {})

        val authentication = Authentication.build(email, roles.map { role -> role.roleName }, serializedAttributes)

        return tokenGenerator.generate(authentication).get().accessToken
    }

}