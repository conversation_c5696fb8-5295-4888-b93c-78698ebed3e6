package cz.deuss.platform.offchain.test.assert

import java.util.function.BiPredicate
import org.assertj.core.api.AbstractAssert
import cz.deuss.platform.offchain.framework.api.model.HttpError
import org.assertj.core.api.Assertions
import org.opentest4j.AssertionFailedError

class HttpErrorAssert(actual: HttpError) :
    AbstractAssert<HttpErrorAssert, HttpError>(actual, HttpErrorAssert::class.java) {

    fun errorEqualsTo(expected: HttpError): HttpErrorAssert {
        isNotNull
        Assertions.assertThat(actual)
            .usingRecursiveComparison()
            .ignoringFields(HttpError::timestamp.name)
            .withEqualsForFields(ERROR_DETAIL_PREDICATE, HttpError::detail.name)
            .isEqualTo(expected)
        Assertions.assertThat(actual.timestamp).isAfter(expected.timestamp)
        return this
    }

    override fun isEqualTo(expected: Any): HttpErrorAssert {
        isNotNull
        if (expected !is HttpError) {
            throw AssertionFailedError("Passed parameter is not an ${HttpError::class.qualifiedName} but is ${expected::class.qualifiedName}")
        }
        return errorEqualsTo(expected)
    }

    companion object {
        fun assertThat(actual: HttpError): HttpErrorAssert = HttpErrorAssert(actual)

        private val ERROR_DETAIL_PREDICATE: BiPredicate<String, String> = BiPredicate { actual, expected ->
            val actualErrors = actual.split(',').map { it.trim() }
            val expectedErrors = expected.split(',').map { it.trim() }
            actualErrors.containsAll(expectedErrors) && expectedErrors.containsAll(actualErrors)
        }
    }
}