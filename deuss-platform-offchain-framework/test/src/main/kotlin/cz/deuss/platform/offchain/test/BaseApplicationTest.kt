package cz.deuss.platform.offchain.test

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.api.model.HttpError
import cz.deuss.platform.offchain.framework.api.model.MessageDetail
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.test.bean.BlockingHttpClientFactory
import cz.deuss.platform.offchain.test.bean.TokenGenerator
import io.micronaut.context.annotation.Import
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.test.support.TestPropertyProvider
import jakarta.inject.Inject
import jakarta.inject.Named
import java.net.URI
import java.time.LocalDateTime
import java.util.UUID
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName

@Import(classes = [BlockingHttpClientFactory::class])
abstract class BaseApplicationTest(
    private val databaseName: String,
    private val username: String = "deuss-platform-service",
    private val port: Int = 5432,
    private val password: String = "Tung Tung Tung Sahur",
) : TestPropertyProvider {

    @Inject
    @set:Named("blockingHttpClient")
    protected lateinit var httpClient: BlockingHttpClient

    @Inject
    @set:Named("exposedHttpClient")
    protected lateinit var exposedClient: BlockingHttpClient

    @Inject
    private lateinit var tokenGenerator: TokenGenerator


    override fun getProperties(): Map<String, String> {
        initPostgresContainer(databaseName, username, port, password)
        if (!postgresContainer.isRunning) {
            postgresContainer.start()
        }
        return mapOf(
            "datasources.default.url" to postgresContainer.jdbcUrl,
            "datasources.default.username" to postgresContainer.username,
            "datasources.default.password" to postgresContainer.password,
        )
    }

    protected fun HttpClientResponseException.extractError(): HttpError =
        response
            .getBody(HttpError::class.java)
            .orElseThrow { IllegalStateException("Unable to extract error from response") }

    private fun createError(
        status: HttpStatus,
        type: URI,
        detail: String,
        messageDetails: List<MessageDetail> = emptyList(),
        instance: URI? = null,
        includeExposedApiPrefix: Boolean = true,
    ): HttpError {
        val errorInstance = if (includeExposedApiPrefix && instance != null) {
            URI.create("${ExposedController.API_PREFIX}/${instance.toString().removePrefix("/")}")
        } else {
            instance
        }

        return HttpError(
            type = type,
            status = status.code,
            title = status.reason,
            messageDetails = messageDetails,
            instance = errorInstance,
            timestamp = LocalDateTime.now(),
            detail = detail
        )
    }

    protected fun createNotFoundError(
        detail: String,
        messageDetails: List<MessageDetail> = emptyList(),
        instance: URI? = null,
        includeExposedApiPrefix: Boolean = true,
    ): HttpError = createError(
        type = URI.create("https://problems-registry.smartbear.com/not-found"),
        status = HttpStatus.NOT_FOUND,
        messageDetails = messageDetails,
        instance = instance,
        detail = detail,
        includeExposedApiPrefix = includeExposedApiPrefix
    )

    protected fun createInternalServerError(
        instance: URI? = null,
        includeExposedApiPrefix: Boolean = true,
    ): HttpError = createError(
        type = URI.create("https://problems-registry.smartbear.com/server-error"),
        status = HttpStatus.INTERNAL_SERVER_ERROR,
        detail = "Internal Server Error",
        messageDetails = emptyList(),
        instance = instance,
        includeExposedApiPrefix = includeExposedApiPrefix
    )


    protected fun createBadRequestError(
        detail: String,
        messageDetails: List<MessageDetail> = emptyList(),
        instance: URI? = null,
        includeExposedApiPrefix: Boolean = true,
    ): HttpError = createError(
        type = URI.create("https://problems-registry.smartbear.com/bad-request"),
        status = HttpStatus.BAD_REQUEST,
        messageDetails = messageDetails,
        instance = instance,
        detail = detail,
        includeExposedApiPrefix = includeExposedApiPrefix
    )

    protected fun createConflictError(
        detail: String,
        messageDetails: List<MessageDetail> = emptyList(),
        instance: URI? = null,
        includeExposedApiPrefix: Boolean = true,
    ): HttpError = createError(
        type = URI.create("https://problems-registry.smartbear.com/conflict"),
        status = HttpStatus.CONFLICT,
        messageDetails = messageDetails,
        instance = instance,
        detail = detail,
        includeExposedApiPrefix = includeExposedApiPrefix,
    )

    protected fun createForbiddenError(
        detail: String,
        messageDetails: List<MessageDetail> = emptyList(),
        instance: URI? = null,
        includeExposedApiPrefix: Boolean = true,
    ): HttpError = createError(
        type = URI.create("https://problems-registry.smartbear.com/forbidden"),
        status = HttpStatus.FORBIDDEN,
        messageDetails = messageDetails,
        instance = instance,
        detail = detail,
        includeExposedApiPrefix = includeExposedApiPrefix
    )

    protected fun generateToken(
        userId: UUID = TEST_USER_ID,
        email: String = "<EMAIL>",
        roles: Set<Role.Enum> = setOf(Role.Enum.USER),
    ) = tokenGenerator.generateToken(userId, email, roles)

    protected companion object {
        private lateinit var postgresContainer: PostgreSQLContainer<*>
        val TEST_USER_ID: UUID = UUID.fromString("94c0914b-de2f-4f3c-be31-80f398ad11fb")

        private fun initPostgresContainer(
            databaseName: String,
            username: String,
            port: Int,
            password: String,
        ) {
            if (!Companion::postgresContainer.isInitialized) {
                postgresContainer = PostgreSQLContainer(DockerImageName.parse("postgres:latest"))
                    .withExposedPorts(port)
                    .withDatabaseName(databaseName)
                    .withPassword(password)
                    .withUsername(username)
            }
        }

    }

}
