package cz.deuss.platform.offchain.test.bean

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory
import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.annotation.Client
import jakarta.inject.Named

@Factory
class BlockingHttpClientFactory {
    @Bean
    @Named("blockingHttpClient")
    fun blockingHttpClient(@Client("/") httpClient: HttpClient): BlockingHttpClient = httpClient.toBlocking()

    @Bean
    @Named("exposedHttpClient")
    fun exposedHttpClient(@Client(ExposedController.API_PREFIX) httpClient: HttpClient): BlockingHttpClient = httpClient.toBlocking()
}
