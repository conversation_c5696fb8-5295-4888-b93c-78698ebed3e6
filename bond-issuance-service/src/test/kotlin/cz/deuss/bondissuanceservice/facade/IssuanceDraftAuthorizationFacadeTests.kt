package cz.deuss.bondissuanceservice.facade


import cz.deuss.bondissuanceservice.api.model.IssuanceDraftUpdate
import cz.deuss.bondissuanceservice.client.ApiCallResult
import cz.deuss.bondissuanceservice.client.company.CompanyEmployeeFetchStatus
import cz.deuss.bondissuanceservice.client.company.InternalCompaniesClient
import cz.deuss.bondissuanceservice.client.employee.InternalEmployeesClient
import cz.deuss.bondissuanceservice.data.convertor.toApi
import cz.deuss.bondissuanceservice.domain.company.Company
import cz.deuss.bondissuanceservice.domain.issuance.CountryCode
import cz.deuss.bondissuanceservice.domain.issuance.Currency
import cz.deuss.bondissuanceservice.domain.issuance.IndustryCode
import cz.deuss.bondissuanceservice.domain.issuance.InterestPaymentRate
import cz.deuss.bondissuanceservice.domain.issuance.IssuanceDraftCreate
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraft
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceState
import cz.deuss.bondissuanceservice.facade.authorization.IssuanceDraftAuthorizationFacade
import cz.deuss.bondissuanceservice.facade.authorization.IssuanceDraftError
import cz.deuss.bondissuanceservice.service.CompanyService
import cz.deuss.bondissuanceservice.service.IssuanceDraftService
import cz.deuss.bondissuanceservice.service.toDomain
import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.authentication.AuthenticationService
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.validation.Invalid
import cz.deuss.platform.offchain.framework.validation.Valid
import io.mockk.Ordering
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class IssuanceDraftAuthorizationFacadeTests {

    private val issuanceDraftServiceMock = mockk<IssuanceDraftService>()
    private val internalCompaniesClientMock = mockk<InternalCompaniesClient>()
    private val internalEmployeesClientMock = mockk<InternalEmployeesClient>()
    private val companyServiceMock = mockk<CompanyService>()


    private val userId = UUID.randomUUID()
    private val issuanceDraftId = UUID.randomUUID()

    private val issuer = Company(
        id = UUID.randomUUID(),
        name = "Blockchain loves me and I love blockchain"
    )
    private val issuance = IssuanceDraft(
        id = issuanceDraftId,
        state = IssuanceState.DRAFT,
        scoring = null,
        name = "",
        description = "",
        isin = null,
        country = CountryCode.AD,
        bondCount = 55144,
        bondNominalValue = BigDecimal("12584.55"),
        interest = BigDecimal("0.50"),
        issueDate = LocalDate.now().plusYears(1),
        currency = Currency.DKK,
        interestPaymentType = InterestPaymentRate.ANNUAL,
        maturity = LocalDate.now().plusYears(6),
        issuer = issuer,
        industryCode = IndustryCode.T,
        created = LocalDateTime.now(),
        lastEdit = LocalDateTime.now(),
        totalBondMoneyVolume = BigDecimal("12584.55").multiply(BigDecimal.valueOf(55144L))
    )


    private var userRoles = setOf(Role.Enum.ADMIN)

    val testedFacade = IssuanceDraftAuthorizationFacade(
        issuanceDraftService = issuanceDraftServiceMock,
        internalCompaniesClient = internalCompaniesClientMock,
        internalEmployeesClient = internalEmployeesClientMock,
        companyService = companyServiceMock,
        authenticatedUserData = {
            AuthenticationService.AuthenticationData(
                AuthenticatedUserId(userId),
                "<EMAIL>",
                userRoles
            )
        }
    )

    @Test
    fun `Deuss admin can fetch issuance draft`() {
        every { issuanceDraftServiceMock.getIssuanceDraft(issuanceDraftId) } returns issuance

        val returnedIssuanceDraft = testedFacade.getIssuanceDraft(issuanceDraftId)

        assertThat(returnedIssuanceDraft is Valid).isTrue
        val issuanceDraft = returnedIssuanceDraft as Valid
        assertThat(issuanceDraft.data).usingRecursiveComparison().isEqualTo(issuance)
        verify(exactly = 1) { issuanceDraftServiceMock.getIssuanceDraft(issuanceDraftId) }
    }


    @Test
    fun `Deuss administrator can create bond issuance`() {
        every { companyServiceMock.getCompanyById(issuer.id) } returns issuer
        val issuanceDraftCreate = IssuanceDraftCreate(issuer, issuance.name, issuance.country)
        every { issuanceDraftServiceMock.createIssuanceDraft(issuanceDraftCreate) } returns issuance

        val returnedIssuanceDraft = testedFacade.createIssuanceDraft(
            cz.deuss.bondissuanceservice.api.model.IssuanceDraftCreate(
                issuance.name,
                issuance.country.toApi(),
                issuer.id
            )
        )

        assertThat(returnedIssuanceDraft is Valid).isTrue
        val issuanceDraft = returnedIssuanceDraft as Valid
        assertThat(issuanceDraft.data).usingRecursiveComparison().isEqualTo(issuance)
        verify(ordering = Ordering.SEQUENCE) {
            companyServiceMock.getCompanyById(issuer.id)
            issuanceDraftServiceMock.createIssuanceDraft(issuanceDraftCreate)
        }
    }


    @Test
    fun `Deuss admin can delete issuance draft`() {
        every { issuanceDraftServiceMock.getIssuanceDraft(issuanceDraftId) } returns issuance
        every { issuanceDraftServiceMock.deleteIssuanceDraftById(issuanceDraftId) } returns true

        val response = testedFacade.deleteIssuanceDraft(issuanceDraftId)

        assertThat(response is Valid).isTrue

        verify(ordering = Ordering.SEQUENCE) {
            issuanceDraftServiceMock.getIssuanceDraft(issuanceDraftId)
            issuanceDraftServiceMock.deleteIssuanceDraftById(issuanceDraftId)
        }
    }


    @Test
    fun `draft can be updated by Deuss admin`() {
        every { issuanceDraftServiceMock.getIssuanceDraft(issuanceDraftId) } returns issuance
        every {
            issuanceDraftServiceMock.updateIssuanceDraft(
                issuance.id,
                IssuanceDraftUpdate().toDomain()
            )
        } returns Valid(issuance)

        val response = testedFacade.updateIssuanceDraft(issuance.id, IssuanceDraftUpdate())

        assertThat(response is Valid).isTrue
        verify(ordering = Ordering.SEQUENCE) {
            issuanceDraftServiceMock.getIssuanceDraft(issuanceDraftId)
            issuanceDraftServiceMock.updateIssuanceDraft(
                issuance.id,
                IssuanceDraftUpdate().toDomain()
            )
        }
    }

    @Test
    fun `drafts filtered by non existent company returns 403`() {
        every { internalCompaniesClientMock.getCompanyEmployeeStatus(
            userId = userId,
            companyId = issuer.id
        )} returns ApiCallResult.Success(CompanyEmployeeFetchStatus.CompanyNotFoundError(""))

        val returnedIssuanceDraft = testedFacade.getIssuanceDraftsForEmployeeAndCompany(
            page = DEFAULT_PAGE,
            pageSize = DEFAULT_PAGE_SIZE,
            companyId = issuer.id
        )

        assertThat(returnedIssuanceDraft is Invalid).isTrue
        val issuanceDraft = returnedIssuanceDraft as Invalid

        assertThat(issuanceDraft.error).isEqualTo(IssuanceDraftError.Forbidden.USER_CANNOT_ACCESS_ISSUANCE)
    }

    @Test
    fun `drafts filtered by company where caller is not an employee returns 403`() {
        every { internalCompaniesClientMock.getCompanyEmployeeStatus(
            userId = userId,
            companyId = issuer.id
        )} returns ApiCallResult.Success(CompanyEmployeeFetchStatus.UserIsNotEmployeeError(""))

        val returnedIssuanceDraft = testedFacade.getIssuanceDraftsForEmployeeAndCompany(
            page = DEFAULT_PAGE,
            pageSize = DEFAULT_PAGE_SIZE,
            companyId = issuer.id
        )

        assertThat(returnedIssuanceDraft is Invalid).isTrue
        val issuanceDraft = returnedIssuanceDraft as Invalid

        assertThat(issuanceDraft.error).isEqualTo(IssuanceDraftError.Forbidden.USER_CANNOT_ACCESS_ISSUANCE)
    }

    private companion object {
        private const val DEFAULT_PAGE_SIZE = 25
        private const val DEFAULT_PAGE = 0
    }

}