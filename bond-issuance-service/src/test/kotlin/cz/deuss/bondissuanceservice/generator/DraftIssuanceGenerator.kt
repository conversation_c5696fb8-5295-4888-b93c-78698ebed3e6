package cz.deuss.bondissuanceservice.generator

import cz.deuss.bondissuanceservice.database.model.CompanyEntity
import cz.deuss.bondissuanceservice.database.model.draft.IssuanceDraftEntity
import cz.deuss.bondissuanceservice.domain.issuance.CountryCode
import cz.deuss.bondissuanceservice.domain.issuance.Currency
import cz.deuss.bondissuanceservice.domain.issuance.IndustryCode
import cz.deuss.bondissuanceservice.domain.issuance.InterestPaymentRate
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceState
import cz.deuss.bondissuanceservice.domain.issuance.draft.Scoring
import io.micronaut.context.annotation.Bean
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.random.Random

@Bean
internal class DraftIssuanceGenerator(
    private val nameGenerator: NameGenerator,
) {

    fun generateIssuances(count: Int, issuers: List<CompanyEntity>): List<IssuanceDraftEntity> {
        check(count > 0) { "Count must be greater than zero" }
        check(issuers.isNotEmpty()) { "No issuers" }
        return List(count) {
            val issuer = issuers.random(RND)
            generateIssuance(issuer = issuer)
        }
    }


    fun generateIssuance(issuer: CompanyEntity): IssuanceDraftEntity =
        IssuanceDraftEntity(
            UUID.randomUUID(),
            state = IssuanceState.DRAFT,
            scoring = Scoring.A,
            name = nameGenerator.generateRandomName(),
            description = "",
            bondNominalValue = BigDecimal("127454.82"),
            isin = null,
            interestPaymentType = InterestPaymentRate.ANNUAL,
            currency = Currency.HUF,
            issuer = issuer,
            tags = setOf(),
            maturity = LocalDate.now().plusYears(5),
            interest = BigDecimal("10.00"),
            issueDate = LocalDate.now(),
            bondCount = 125411,
            industryCode = IndustryCode.N,
            country = CountryCode.CZ
        )

    private companion object {
        private val RND = Random(23)
    }
}