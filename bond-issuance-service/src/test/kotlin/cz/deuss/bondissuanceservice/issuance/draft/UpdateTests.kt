package cz.deuss.bondissuanceservice.issuance.draft

import cz.deuss.bondissuanceservice.BaseBondIssuanceTest
import cz.deuss.bondissuanceservice.api.model.Currency
import cz.deuss.bondissuanceservice.api.model.InterestPaymentRate
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftResponse
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftUpdate
import cz.deuss.bondissuanceservice.database.model.draft.IssuanceDraftEntity
import cz.deuss.bondissuanceservice.service.toDto
import cz.deuss.bondissuanceservice.utils.withTruncatedLocalDateTimeEquals
import cz.deuss.companyservice.internal.model.EmployeeRole
import cz.deuss.platform.offchain.framework.api.model.HttpError
import cz.deuss.platform.offchain.framework.database.findByIdOrNull
import cz.deuss.platform.offchain.test.assert.HttpErrorAssert
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.simple.SimpleHttpRequest
import io.micronaut.test.annotation.Sql
import java.math.BigDecimal
import java.net.URI
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.hibernate.Hibernate
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

@Sql(
    scripts = ["classpath:sql/base_init.sql", "classpath:sql/issuance_drafts.sql"],
    phase = Sql.Phase.BEFORE_EACH
)
@Sql(scripts = ["classpath:sql/clear_database.sql"], phase = Sql.Phase.AFTER_EACH)
class UpdateTests : BaseBondIssuanceTest() {

    @ParameterizedTest
    @EnumSource(value = EmployeeRole::class, names = ["VIEWER"], mode = EnumSource.Mode.EXCLUDE)
    fun `draft can be updated by company admin and editor`(role: EmployeeRole) {
        val userId = UUID.randomUUID()
        val draftBeforeUpdate = findDraftByIdWithTags(DRAFT_ISSUANCE_ID)
        val request =
            SimpleHttpRequest(
                HttpMethod.PATCH,
                buildPath(ISSUANCES_DRAFTS_PATH, DRAFT_ISSUANCE_ID),
                VALID_UPDATE_REQUEST_BODY
            )
                .bearerAuth(generateToken(userId))
        companyMockServer.enqueueCompanySummaryResponse(
            companyId = draftBeforeUpdate.issuer.id,
            userId = userId,
            employeeRole = role
        )

        val response = exposedClient.exchange(request, Argument.of(IssuanceDraftResponse::class.java))

        assertThat(response.status).isEqualTo(HttpStatus.OK)
        assertThat(response.body()).isNotNull
        val draftAfterUpdate = findDraftByIdWithTags(DRAFT_ISSUANCE_ID)
        assertThat(response.body())
            .usingRecursiveComparison()
            .withTruncatedLocalDateTimeEquals()
            .isEqualTo(draftAfterUpdate.toDto())
        assertThat(draftAfterUpdate)
            .usingRecursiveComparison()
            .withEqualsForType(DATETIME_EQUALS, LocalDateTime::class.java)
            .isNotEqualTo(draftBeforeUpdate)
        assertThat(draftAfterUpdate)
            .usingRecursiveComparison()
            .ignoringFields(
                IssuanceDraftEntity::id.name,
                IssuanceDraftEntity::country.name,
                IssuanceDraftEntity::scoring.name,
                IssuanceDraftEntity::created.name,
                IssuanceDraftEntity::issuer.name,
                IssuanceDraftEntity::tags.name,
                IssuanceDraftEntity::name.name,
                IssuanceDraftEntity::state.name,
                IssuanceDraftEntity::lastEdit.name,
                IssuanceDraftEntity::isin.name,
            ).isEqualTo(VALID_UPDATE_REQUEST_BODY)
        assertThat(draftAfterUpdate.isin).isEqualTo(VALID_UPDATE_REQUEST_BODY.ISIN)
        companyMockServer.mockServer.assertAllResponsesUsed()
    }

    @Test
    fun `not found error is returned when issuance draft doesn't exist`() {
        val updateRequestBody = IssuanceDraftUpdate()
        val path = buildPath(ISSUANCES_DRAFTS_PATH, NON_EXISTING_DRAFT_ID)
        val request = SimpleHttpRequest(HttpMethod.PATCH, path, updateRequestBody).bearerAuth(generateToken())
        val expectedError = createForbiddenError(detail = "User cannot update issuance draft", instance = URI(path))

        val response = assertThrows<HttpClientResponseException> {
            exposedClient.exchange(
                request,
                Argument.of(IssuanceDraftResponse::class.java),
                Argument.of(HttpError::class.java)
            )
        }

        assertThat(response.status).isEqualTo(HttpStatus.FORBIDDEN)
        HttpErrorAssert.assertThat(response.extractError()).isEqualTo(expectedError)
    }


    @Test
    fun `only not null fields are updated`() {
        val userId = UUID.randomUUID()
        val draftBeforeUpdate = findDraftByIdWithTags(DRAFT_ISSUANCE_ID)
        val updateRequestBody = IssuanceDraftUpdate(
            interest = BigDecimal("10.52"),
            description = "Nissan Navara není tak dobrý Navara jako Mirko Navara.",
            bondNominalValue = BigDecimal("88.88"),
        )
        val updatedFields = arrayOf(
            IssuanceDraftEntity::interest.name,
            IssuanceDraftEntity::description.name,
            IssuanceDraftEntity::bondNominalValue.name
        )
        companyMockServer.enqueueCompanySummaryResponse(
            companyId = draftBeforeUpdate.issuer.id,
            userId = userId,
        )

        val request =
            SimpleHttpRequest(HttpMethod.PATCH, buildPath(ISSUANCES_DRAFTS_PATH, DRAFT_ISSUANCE_ID), updateRequestBody)
                .bearerAuth(generateToken(userId))

        val response = exposedClient.exchange(
            request,
            Argument.of(IssuanceDraftResponse::class.java),
        )

        assertThat(response.status).isEqualTo(HttpStatus.OK)
        assertThat(response.body()).isNotNull
        val draftAfterUpdate = findDraftByIdWithTags(DRAFT_ISSUANCE_ID)
        assertThat(response.body()).usingRecursiveComparison()
            .withTruncatedLocalDateTimeEquals()
            .isEqualTo(draftAfterUpdate.toDto())
        assertThat(draftAfterUpdate)
            .usingRecursiveComparison()
            .withEqualsForType(DATETIME_EQUALS, LocalDateTime::class.java)
            .ignoringFields(*updatedFields)
            .isEqualTo(draftBeforeUpdate)
        assertThat(draftAfterUpdate)
            .usingRecursiveComparison()
            .comparingOnlyFields(*updatedFields)
            .isEqualTo(updateRequestBody)
    }

    @ParameterizedTest
    @MethodSource("invalidInputProvider")
    fun `invalid attributes return error`(requestBody: IssuanceDraftUpdate, expectedErrorMessage: String) {
        val userId = UUID.randomUUID()
        val path = buildPath(ISSUANCES_DRAFTS_PATH, DRAFT_ISSUANCE_ID)
        val invalidRequest = SimpleHttpRequest(HttpMethod.PATCH, path, requestBody)
            .bearerAuth(generateToken(userId))

        companyMockServer.enqueueCompanySummaryResponse(
            companyId = COMPANY_ID,
            userId = userId,
        )

        val expectedError = createBadRequestError(detail = expectedErrorMessage, instance = URI(path))

        val response = assertThrows<HttpClientResponseException> {
            exposedClient.exchange(
                invalidRequest,
                Argument.of(IssuanceDraftResponse::class.java),
                Argument.of(HttpError::class.java)
            )
        }

        assertThat(response.status).isEqualTo(HttpStatus.BAD_REQUEST)
        HttpErrorAssert.assertThat(response.extractError()).isEqualTo(expectedError)
    }


    private fun findDraftByIdWithTags(issuanceId: UUID): IssuanceDraftEntity = transactionHelper.runInTransaction {
        val entity = issuanceDraftRepository.findByIdOrNull(issuanceId)!!
        Hibernate.initialize(entity.tags)
        entity
    }

    companion object {
        private val NON_EXISTING_DRAFT_ID = UUID.fromString("831f97bc-db30-4ccb-bdb0-b0f3bd3e6c94")

        @JvmStatic
        private fun invalidInputProvider(): Stream<Arguments> = Stream.of(
            Arguments.of(
                IssuanceDraftUpdate(
                    maturity = LocalDate.now().minusDays(100),
                    issueDate = LocalDate.now(),
                ),
                "updateIssuanceDraft.issuanceDraftUpdate.maturity: Field maturity must be in future., updateIssuanceDraft.issuanceDraftUpdate.issueDate: Field issue_date must be in future."
            ),
            Arguments.of(
                IssuanceDraftUpdate(
                    description = "",
                ), "updateIssuanceDraft.issuanceDraftUpdate.description: when not null, it must be not blank"
            ),
            Arguments.of(
                IssuanceDraftUpdate(
                    description = generateRandomString(100_000)
                ),
                "updateIssuanceDraft.issuanceDraftUpdate.description: size must be between 0 and 10000"
            ),
            Arguments.of(
                IssuanceDraftUpdate(
                    bondNominalValue = BigDecimal("0.00"),
                    interest = BigDecimal("-100.00"),
                ),
                "updateIssuanceDraft.issuanceDraftUpdate.interest: Provided value must be at least 0.01, updateIssuanceDraft.issuanceDraftUpdate.bondNominalValue: Provided value must be at least 0.01"
            ),
            Arguments.of(
                IssuanceDraftUpdate(
                    bondNominalValue = BigDecimal("57475757457545745587574574527545242475545483521853830.00"),
                    interest = BigDecimal("85.05854"),
                ),
                "updateIssuanceDraft.issuanceDraftUpdate.interest: Value must have at most 24 digits and at most 2 digits after decimal point, updateIssuanceDraft.issuanceDraftUpdate.bondNominalValue: Value must have at most 24 digits and at most 2 digits after decimal point"
            ),
            Arguments.of(
                IssuanceDraftUpdate(
                    bondCount = -55
                ),
                "updateIssuanceDraft.issuanceDraftUpdate.bondCount: must be greater than or equal to 0"
            ),
            Arguments.of(
                IssuanceDraftUpdate(
                    ISIN = "asdadjajdajal"
                ),
                "updateIssuanceDraft.issuanceDraftUpdate.ISIN: when not null, it must be valid ISIN"
            ),
        )

        private val VALID_UPDATE_REQUEST_BODY = IssuanceDraftUpdate(
            maturity = LocalDate.now().plusYears(100),
            description = "If Lenin could see us now!",
            currency = Currency.HUF,
            bondCount = 69420,
            interest = BigDecimal("10.52"),
            interestPaymentType = InterestPaymentRate.ANNUAL,
            issueDate = LocalDate.now().plusMonths(69),
            bondNominalValue = BigDecimal("85474.38"),
            ISIN = "DE000BAY0017"
        )

    }
}