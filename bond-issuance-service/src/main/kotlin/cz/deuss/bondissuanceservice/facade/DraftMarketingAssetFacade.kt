package cz.deuss.bondissuanceservice.facade

import cz.deuss.bondissuanceservice.api.model.MarketingAssetFileType
import cz.deuss.bondissuanceservice.client.ApiCallResult
import cz.deuss.bondissuanceservice.client.DocumentDeleteStatus
import cz.deuss.bondissuanceservice.client.FileServiceClient
import cz.deuss.bondissuanceservice.data.PagedResult
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraftId
import cz.deuss.bondissuanceservice.domain.issuance.draft.marketingAsset.DraftMarketingAsset
import cz.deuss.bondissuanceservice.domain.issuance.draft.marketingAsset.DraftMarketingAssetCreate
import cz.deuss.bondissuanceservice.domain.issuance.draft.marketingAsset.DraftMarketingAssetId
import cz.deuss.bondissuanceservice.domain.user.UserId
import cz.deuss.bondissuanceservice.service.DraftMarketingAssetService
import cz.deuss.bondissuanceservice.validation.IssuanceDraftValidator
import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.platform.offchain.framework.validation.Invalid
import cz.deuss.platform.offchain.framework.validation.Valid
import cz.deuss.platform.offchain.framework.validation.ValidationResult
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.net.URI
import java.time.LocalDateTime
import java.util.UUID

@Singleton
open class DraftMarketingAssetFacade(
    private val issuanceDraftValidator: IssuanceDraftValidator,
    private val draftMarketingAssetService: DraftMarketingAssetService,
    private val fileServiceClient: FileServiceClient,
    private val authUserId: () -> AuthenticatedUserId,
) {

    fun saveNewMarketingAssetForIssuanceDraft(
        issuanceDraftId: UUID,
        draftMarketingAssetCreate: DraftMarketingAssetCreate,
    ): ValidationResult<UploadDocumentError, DraftMarketingAssetDetailDto> {
        val saveResult = draftMarketingAssetService.saveNewMarketingAssetForIssuanceDraft(
            issuanceDraftId = issuanceDraftId,
            draftMarketingAssetCreate = draftMarketingAssetCreate,
            creator = UserId(authUserId().id)
        )

        return when (saveResult) {
            is Valid -> {
                handleUploadSafely(saveResult.data)
            }

            is Invalid -> {
                Invalid(UploadDocumentError.IssuanceDraftNotFound)
            }
        }
    }

    fun getMarketingAssetById(
        marketingAssetId: UUID,
    ): ValidationResult<DownloadDocumentError, DraftMarketingAssetDetailDto> {
        val draft = draftMarketingAssetService.getMarketingAssetById(marketingAssetId)
            ?: return Invalid(DownloadDocumentError.DocumentNotFound)
        return when (val downloadUrlResult = fileServiceClient.getDownloadUrl(draft.dmsPath, draft.fullFilename)) {
            is ApiCallResult.Success -> {
                Valid(draft.toDto(downloadUrlResult.data))
            }

            is ApiCallResult.UnrecoverableError -> {
                Invalid(DownloadDocumentError.Unrecoverable(downloadUrlResult.toApiError()))
            }
        }
    }

    @Transactional(readOnly = true)
    open fun getPagedMarketingAssetsForIssuanceDraft(
        issuanceDraftId: UUID,
        page: Int,
        pageSize: Int,
    ): PagedResult<DownloadDraftMarketingAsset>? {
        val validatedIssuanceDraftId = issuanceDraftValidator.getIssuanceDraftExistence(issuanceDraftId) ?: return null

        val pagedAssets = draftMarketingAssetService.getMarketingAssetsByIssuanceDraftId(validatedIssuanceDraftId, page, pageSize)

        val assetsWithDownloadLinks = pagedAssets.data.mapNotNull { asset ->
            when (val downloadUrlResult = fileServiceClient.getDownloadUrl(asset.dmsPath, asset.fullFilename)) {
                is ApiCallResult.Success -> {
                    DownloadDraftMarketingAsset(
                        marketingAsset = asset,
                        downloadLink = downloadUrlResult.data
                    )
                }
                is ApiCallResult.UnrecoverableError -> {
                    logger.message("Failed to get download URL for marketing asset")
                        .param("assetId", asset.id.id)
                        .param("error", downloadUrlResult.toApiError().toString()).error()
                    null
                }
            }
        }

        return PagedResult(
            page = pagedAssets.page,
            count = pagedAssets.count,
            data = assetsWithDownloadLinks
        )
    }

    fun deleteMarketingAsset(draftMarketingAssetId: UUID): ValidationResult<DeleteDocumentError, Unit> {
        val marketingAsset = draftMarketingAssetService.getMarketingAssetById(draftMarketingAssetId)
            ?: return Invalid(DeleteDocumentError.DocumentNotFound)

        return when (val deleteResponse = fileServiceClient.deleteFile(marketingAsset.dmsPath)) {
            is ApiCallResult.Success -> {
                Valid(handleDeleteMarketingAssetStatus(deleteResponse.data, draftMarketingAssetId))
            }

            is ApiCallResult.UnrecoverableError -> {
                Invalid(DeleteDocumentError.Unrecoverable(deleteResponse.toApiError()))
            }
        }
    }


    private fun handleUploadSafely(draftMarketingAsset: DraftMarketingAsset): ValidationResult<UploadDocumentError, DraftMarketingAssetDetailDto> {
        return when (val putUrl = fileServiceClient.getUploadUrl(draftMarketingAsset.dmsPath)) {
            is ApiCallResult.Success -> Valid(draftMarketingAsset.toDto(putUrl.data))
            is ApiCallResult.UnrecoverableError -> {
                draftMarketingAssetService.deleteById(draftMarketingAsset.id.id)
                Invalid(UploadDocumentError.Unrecoverable(putUrl.toApiError()))
            }
        }
    }

    private fun handleDeleteMarketingAssetStatus(deleteStatus: DocumentDeleteStatus, draftMarketingAssetId: UUID) {
        when (deleteStatus) {
            is DocumentDeleteStatus.Deleted -> {
                draftMarketingAssetService.deleteById(draftMarketingAssetId)
            }

            is DocumentDeleteStatus.NotFound -> {
                if (draftMarketingAssetService.deleteById(draftMarketingAssetId)) {
                    logger.message("Deleted dangling reference in database to draft document")
                        .param("documentId", draftMarketingAssetId).info()
                }
            }
        }
    }

    companion object {
        private val logger = DeussLogger.semanticLogger(DraftMarketingAssetFacade::class)
    }
}

data class DraftMarketingAssetDetailDto(
    val id: DraftMarketingAssetId,
    val name: String,
    val fileType: MarketingAssetFileType,
    val creatorId: UserId,
    val issuanceDraftId: IssuanceDraftId,
    val dmsPath: URI,
    val createdAt: LocalDateTime,
)

class DownloadDraftMarketingAsset(
    val marketingAsset: DraftMarketingAsset,
    val downloadLink: URI?,
)

fun DraftMarketingAsset.toDto(dmsLink: URI): DraftMarketingAssetDetailDto {
    return DraftMarketingAssetDetailDto(
        id = id,
        creatorId = creatorId,
        name = name,
        issuanceDraftId = issuanceDraftId,
        dmsPath = dmsLink,
        fileType = type,
        createdAt = createdAt,
    )
}