package cz.deuss.bondissuanceservice.database.model.draft

import cz.deuss.bondissuanceservice.database.model.CompanyEntity
import cz.deuss.bondissuanceservice.database.model.TagEntity
import cz.deuss.bondissuanceservice.domain.issuance.CountryCode
import cz.deuss.bondissuanceservice.domain.issuance.Currency
import cz.deuss.bondissuanceservice.domain.issuance.IndustryCode
import cz.deuss.bondissuanceservice.domain.issuance.InterestPaymentRate
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceState
import cz.deuss.bondissuanceservice.domain.issuance.draft.Scoring
import cz.deuss.platform.offchain.framework.database.BaseEntityWithTimestamps
import io.micronaut.core.annotation.Introspected
import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.JoinColumn
import jakarta.persistence.JoinTable
import jakarta.persistence.ManyToMany
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import jakarta.persistence.Transient
import jakarta.validation.constraints.Digits
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.util.UUID

@Entity
@Table(name = "issuance_draft")
@Introspected
class IssuanceDraftEntity(
    id: UUID,

    @field:Column(name = "state", nullable = false, length = 255)
    @field:Enumerated(EnumType.STRING)
    var state: IssuanceState,

    @field:Column(name = "scoring", nullable = true, length = 3)
    @field:Enumerated(EnumType.STRING)
    var scoring: Scoring?,

    @field:Size(max = 255)
    @field:Column(name = "name")
    @field:NotBlank
    var name: String,
    @field:Size(max = 10_000)
    @field:Column(name = "description", length = 10_000)
    var description: String?,

    @field:Size(max = 255)
    @field:Column(name = "isin")
    var isin: String?,

    @field:Column(name = "country", length = 2)
    @field:Enumerated(EnumType.STRING)
    var country: CountryCode,

    @field:Column(name = "bond_count")
    var bondCount: Int?,

    @field:Column(name = "bond_nominal_value", precision = 26, scale = 2)
    @field:Digits(integer = 24, fraction = 2)
    var bondNominalValue: BigDecimal?,

    @field:Column(name = "interest", precision = 26, scale = 2)
    @field:Digits(integer = 24, fraction = 2)
    var interest: BigDecimal?,

    @field:Column(name = "issue_date")
    var issueDate: LocalDate?,

    @field:Column(name = "currency", length = 3)
    @field:Enumerated(EnumType.STRING)
    var currency: Currency?,

    @field:Column(name = "interest_payment_type")
    @field:Enumerated(EnumType.STRING)
    var interestPaymentType: InterestPaymentRate?,

    @field:Column(name = "maturity")
    var maturity: LocalDate?,

    @field:ManyToOne(fetch = FetchType.LAZY, optional = false)
    @field:JoinColumn(name = "issuer_id", nullable = false)
    val issuer: CompanyEntity,

    @field:Column(name = "industry_code", length = 1)
    @field:Enumerated(EnumType.STRING)
    var industryCode: IndustryCode?,

    @field:ManyToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @field:JoinTable(
        name = "issuance_draft_tag_assignment",
        joinColumns = [JoinColumn(name = "issuance_draft_id")],
        inverseJoinColumns = [JoinColumn(name = "tag_id")]
    )
    val tags: Set<TagEntity> = mutableSetOf(),
) : BaseEntityWithTimestamps(id) {
    @get:Transient
    val totalBondMoneyVolume: BigDecimal?
        get() {
            val bondCount = bondCount ?: return null
            val bondNominalValue = bondNominalValue ?: return null
            return bondNominalValue.setScale(2, RoundingMode.HALF_EVEN) * BigDecimal.valueOf(bondCount.toLong())
        }
}