package cz.deuss.bondissuanceservice.service

import cz.deuss.bondissuanceservice.api.model.IssuanceDraftResponse
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftUpdate
import cz.deuss.bondissuanceservice.api.model.PagedIssuanceDraftsResponse
import cz.deuss.bondissuanceservice.client.employee.CompanyEmployeeIds
import cz.deuss.bondissuanceservice.data.convertor.toApi
import cz.deuss.bondissuanceservice.data.convertor.toDomain
import cz.deuss.bondissuanceservice.database.model.CompanyEntity
import cz.deuss.bondissuanceservice.database.model.draft.IssuanceDraftEntity
import cz.deuss.bondissuanceservice.database.repository.IssuanceDraftRepository
import cz.deuss.bondissuanceservice.domain.company.Company
import cz.deuss.bondissuanceservice.domain.company.CompanyId
import cz.deuss.bondissuanceservice.domain.issuance.IssuanceDraftCreate
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraft
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraftPatch
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceState
import cz.deuss.bondissuanceservice.validation.IssuanceDraftValidationResult
import cz.deuss.bondissuanceservice.validation.IssuanceDraftValidator
import cz.deuss.platform.offchain.framework.validation.Invalid
import cz.deuss.platform.offchain.framework.validation.Valid
import cz.deuss.platform.offchain.framework.validation.ValidationResult
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.Sort
import io.micronaut.transaction.annotation.Transactional
import java.util.UUID

open class IssuanceDraftService(
    private val issuanceDraftRepository: IssuanceDraftRepository,
    private val companyService: CompanyService,
    private val issuanceDraftIdGenerator: () -> UUID,
) {

    @Transactional
    open fun deleteIssuanceDraftById(issuanceDraftId: UUID): Boolean {
        val numberOfDeletedRecords = issuanceDraftRepository.deleteEntityById(issuanceDraftId)
        return numberOfDeletedRecords == 1
    }

    @Transactional
    open fun createIssuanceDraft(issuanceDraftCreate: IssuanceDraftCreate): IssuanceDraft {
        val issuer = companyService.getOrSaveCompany(issuanceDraftCreate.company)
        val mappedToEntity = issuanceDraftCreate.toEntity(issuanceDraftIdGenerator(), IssuanceState.DRAFT, issuer)

        return issuanceDraftRepository.saveAndFlush(mappedToEntity).toDomain()
    }


    @Transactional
    open fun updateIssuanceDraft(
        issuanceId: UUID,
        issuanceDraftPatch: IssuanceDraftPatch,
    ): ValidationResult<IssuanceDraftValidationResult.Failure, IssuanceDraft> {
        val issuanceDraftEntity = getIssuanceDraftById(issuanceId)
            ?: return Invalid(IssuanceDraftValidationResult.Failure.IssuanceDraftDoesNotExist("Issuance$issuanceId not found"))
        val currentIssuance = issuanceDraftEntity.toDomain()
        val updatedIssuanceDraft = updateIssuanceDraftAttributes(currentIssuance, issuanceDraftPatch)

        val updatedEntity =
            issuanceDraftRepository.updateById(issuanceId, issuanceDraftEntity.update(updatedIssuanceDraft))
        return Valid(updatedEntity.toDomain())
    }

    @Transactional(readOnly = true)
    open fun getIssuanceDrafts(page: Int, pageSize: Int): PagedIssuanceDraftsResponse {
        val pageable = Pageable.from(page, pageSize, DEFAULT_SORT)
        return issuanceDraftRepository.findAll(pageable).toDto()
    }

    @Transactional(readOnly = true)
    open fun getIssuanceDraft(issuanceId: UUID): IssuanceDraft? {
        return issuanceDraftRepository.getById(issuanceId)?.toDomain()
    }

    @Transactional(readOnly = true)
    open fun getIssuanceDraft(isin: String): IssuanceDraft? {
        return issuanceDraftRepository.getByIsin(isin)?.toDomain()
    }

    @Transactional(readOnly = true)
    open fun fetchIssuanceDraftsForCompanies(
        companiesObservableByUser: CompanyEmployeeIds,
        page: Int,
        pageSize: Int,
    ): PagedIssuanceDraftsResponse {
        val issuerIds = companiesObservableByUser.ids.map { it.id }
        val pageable = Pageable.from(page, pageSize)
        return issuanceDraftRepository.findByIssuerIds(issuerIds, pageable).toDto()
    }

    @Transactional(readOnly = true)
    open fun fetchIssuanceDraftsForCompany(
        issuerId: CompanyId,
        page: Int,
        pageSize: Int,
    ): PagedIssuanceDraftsResponse {
        val pageable = Pageable.from(page, pageSize)
        return issuanceDraftRepository.findByIssuerId(issuerId.id, pageable).toDto()
    }

    @Transactional(readOnly = true)
    open fun getPublishableDrafts(page: Int, pageSize: Int): Page<IssuanceDraftEntity> {
        val pageable = Pageable.from(page, pageSize, DEFAULT_SORT)

        return issuanceDraftRepository.getByState(
            IssuanceState.APPROVED,
            pageable
        )
    }


    private fun getIssuanceDraftById(issuanceId: UUID): IssuanceDraftEntity? {
        return issuanceDraftRepository.getById(issuanceId)
    }

    @Transactional
    fun updateDrafts(drafts: Collection<IssuanceDraftEntity>): Int {
        return issuanceDraftRepository.updateAll(drafts).count()
    }


    private fun updateIssuanceDraftAttributes(current: IssuanceDraft, update: IssuanceDraftPatch): IssuanceDraft {
        return IssuanceDraft(
            id = current.id,
            state = current.state,
            name = update.name ?: current.name,
            isin = update.isin ?: current.isin,
            bondNominalValue = update.bondNominalValue ?: current.bondNominalValue,
            issuer = current.issuer,
            currency = update.currency ?: current.currency,
            industryCode = update.industryCode ?: current.industryCode,
            description = update.description ?: current.description,
            issueDate = update.issueDate ?: current.issueDate,
            interestPaymentType = update.interestPaymentType ?: current.interestPaymentType,
            interest = update.interest ?: current.interest,
            maturity = update.maturity ?: current.maturity,
            bondCount = update.bondCount ?: current.bondCount,
            scoring = update.scoring ?: current.scoring,
            country = current.country,
            created = current.created,
            lastEdit = current.lastEdit,
            totalBondMoneyVolume = current.totalBondMoneyVolume
        )
    }

    private fun IssuanceDraftEntity.update(issuanceDraft: IssuanceDraft): IssuanceDraftEntity {
        this.name = issuanceDraft.name
        this.state = issuanceDraft.state
        this.interestPaymentType = issuanceDraft.interestPaymentType
        this.isin = issuanceDraft.isin
        this.bondNominalValue = issuanceDraft.bondNominalValue
        this.currency = issuanceDraft.currency
        this.description = issuanceDraft.description
        this.interest = issuanceDraft.interest
        this.scoring = issuanceDraft.scoring
        this.maturity = issuanceDraft.maturity
        this.bondCount = issuanceDraft.bondCount
        this.issueDate = issuanceDraft.issueDate
        this.industryCode = issuanceDraft.industryCode
        return this
    }

    private companion object {
        private val DEFAULT_SORT = Sort.of(Sort.Order.asc(IssuanceDraftEntity::id.name))
    }

}


fun IssuanceDraftCreate.toEntity(
    id: UUID,
    state: IssuanceState,
    companyEntity: CompanyEntity,
): IssuanceDraftEntity = IssuanceDraftEntity(
    id = id,
    name = name,
    state = state,
    interestPaymentType = null,
    isin = null,
    bondNominalValue = null,
    issuer = companyEntity,
    currency = null,
    country = country,
    description = null,
    interest = null,
    scoring = null,
    maturity = null,
    bondCount = null,
    issueDate = null,
    industryCode = null
)


fun IssuanceDraftEntity.toDto() = IssuanceDraftResponse(
    id = id,
    name = name,
    state = state.toApi(),
    interestPaymentType = interestPaymentType?.toApi(),
    bondNominalValue = bondNominalValue,
    maturity = maturity,
    issueDate = issueDate,
    interest = interest,
    scoring = scoring?.toApi(),
    currency = currency?.toApi(),
    bondCount = bondCount,
    ISIN = isin,
    lastEdit = lastEdit,
    created = created,
    volume = totalBondMoneyVolume,
    industryCode = industryCode?.toApi(),
    description = description,
    country = country.toApi(),
    companyId = issuer.id
)

fun CompanyEntity.toDomain() = Company(
    id = id,
    name = name,
)

fun IssuanceDraftUpdate.toDomain() = IssuanceDraftPatch(
    maturity = maturity,
    description = description,
    currency = currency?.toDomain(),
    bondNominalValue = bondNominalValue,
    bondCount = bondCount,
    interest = interest,
    scoring = scoring?.toDomain(),
    isin = ISIN,
    interestPaymentType = interestPaymentType?.toDomain(),
    industryCode = industryCode?.toDomain(),
    issueDate = issueDate,
    name = name
)

fun IssuanceDraftEntity.toDomain(): IssuanceDraft {
    return IssuanceDraft(
        id = this.id,
        name = this.name,
        state = this.state,
        description = this.description,
        interest = this.interest,
        scoring = this.scoring,
        maturity = this.maturity,
        bondCount = this.bondCount,
        issueDate = this.issueDate,
        industryCode = this.industryCode,
        isin = this.isin,
        currency = this.currency,
        country = this.country,
        interestPaymentType = this.interestPaymentType,
        bondNominalValue = this.bondNominalValue,
        issuer = this.issuer.toDomain(),
        created = created,
        lastEdit = lastEdit,
        totalBondMoneyVolume = totalBondMoneyVolume
    )
}

fun Page<IssuanceDraftEntity>.toDto() = PagedIssuanceDraftsResponse(
    count = this.totalSize,
    page = this.pageNumber,
    issuanceDrafts = this.content.map { it.toDto() }
)
