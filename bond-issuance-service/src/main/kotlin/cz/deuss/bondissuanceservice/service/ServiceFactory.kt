package cz.deuss.bondissuanceservice.service

import cz.deuss.bondissuanceservice.database.repository.DraftDocumentRepository
import cz.deuss.bondissuanceservice.database.repository.DraftMarketingAssetRepository
import cz.deuss.bondissuanceservice.database.repository.IssuanceDraftRepository
import cz.deuss.bondissuanceservice.validation.IssuanceDraftValidator
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory
import java.util.UUID

@Factory
class ServiceFactory {

    @Bean
    fun issuanceDraftService(
        issuanceDraftRepository: IssuanceDraftRepository,
        companyService: CompanyService,
        issuanceDraftValidator: IssuanceDraftValidator,
    ): IssuanceDraftService {
        return IssuanceDraftService(
            issuanceDraftRepository, companyService
        ) { UUID.randomUUID() }
    }


    @Bean
    fun draftDocumentService(
        issuanceDraftRepository: IssuanceDraftRepository,
        draftDocumentRepository: DraftDocumentRepository,
    ): DraftDocumentService {
        return DraftDocumentService(
            issuanceDraftRepository, draftDocumentRepository
        ) { UUID.randomUUID() }
    }


    @Bean
    fun draftMarketingAssetService(
        issuanceDraftRepository: IssuanceDraftRepository,
        draftMarketingAssetRepository: DraftMarketingAssetRepository,
    ): DraftMarketingAssetService {
        return DraftMarketingAssetService(
            issuanceDraftRepository, draftMarketingAssetRepository
        ) { UUID.randomUUID() }
    }

}
