package cz.deuss.bondissuanceservice.controller

import cz.deuss.bondissuanceservice.api.IssuanceDraftMarketingAssetApi
import cz.deuss.bondissuanceservice.api.model.BondIssuanceDraftMarketingAssetCreateRequest
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftMarketingAssetDetailResponse
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftMarketingAssetSummaryResponse
import cz.deuss.bondissuanceservice.api.model.PagedDraftMarketingAssets
import cz.deuss.bondissuanceservice.domain.issuance.draft.marketingAsset.DraftMarketingAsset
import cz.deuss.bondissuanceservice.domain.issuance.draft.marketingAsset.DraftMarketingAssetCreate
import cz.deuss.bondissuanceservice.facade.DeleteDocumentError
import cz.deuss.bondissuanceservice.facade.DownloadDocumentError
import cz.deuss.bondissuanceservice.facade.DraftMarketingAssetDetailDto
import cz.deuss.bondissuanceservice.facade.DraftMarketingAssetFacade
import cz.deuss.bondissuanceservice.facade.UploadDocumentError
import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.exceptions.InternalServerErrorException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import io.micronaut.http.HttpResponse
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import java.net.URI
import java.util.UUID

@Secured(SecurityRule.IS_AUTHENTICATED)
@ExposedController
@ExecuteOn(TaskExecutors.BLOCKING)
open class IssuanceDraftMarketingAssetController(
    private val draftMarketingAssetFacade: DraftMarketingAssetFacade
) : IssuanceDraftMarketingAssetApi {

    override fun createMarketingAssetForIssuanceDraft(
        issuanceId: UUID,
        bondIssuanceDraftMarketingAssetCreateRequest: BondIssuanceDraftMarketingAssetCreateRequest
    ): HttpResponse<IssuanceDraftMarketingAssetDetailResponse> {
        val createRequest = DraftMarketingAssetCreate(
            bondIssuanceDraftMarketingAssetCreateRequest.marketingAssetName,
            bondIssuanceDraftMarketingAssetCreateRequest.fileType
        )

        val responseBody = draftMarketingAssetFacade.saveNewMarketingAssetForIssuanceDraft(issuanceId, createRequest)
            .validOrThrow {
                when (it) {
                    is UploadDocumentError.IssuanceDraftNotFound -> NotFoundException("Issuance draft not found")
                    is UploadDocumentError.Unrecoverable -> InternalServerErrorException("Unable to provide download url to marketing asset")
                }
            }
        return HttpResponse.created(responseBody.toResponse())
    }

    override fun deleteMarketingAssetById(marketingAssetId: UUID): HttpResponse<Void> {
        draftMarketingAssetFacade.deleteMarketingAsset(marketingAssetId).validOrThrow {
            when (it) {
                is DeleteDocumentError.Unrecoverable -> InternalServerErrorException("Unable to delete marketing asset")
                is DeleteDocumentError.DocumentNotFound -> NotFoundException("Marketing asset not found")
            }
        }
        return HttpResponse.noContent()
    }

    override fun getMarketingAssetDetail(marketingAssetId: UUID): IssuanceDraftMarketingAssetDetailResponse {
        val response = draftMarketingAssetFacade.getMarketingAssetById(marketingAssetId = marketingAssetId).validOrThrow {
            when (it) {
                is DownloadDocumentError.DocumentNotFound -> NotFoundException("Marketing asset draft not found")
                is DownloadDocumentError.Unrecoverable -> InternalServerErrorException("Unable to provide download url to marketing asset")
            }
        }
        return response.toResponse()
    }

    override fun getMarketingAssetsForIssuanceDraft(
        issuanceId: UUID,
        page: Int,
        pageSize: Int
    ): PagedDraftMarketingAssets {
        val response = draftMarketingAssetFacade.getPagedMarketingAssetsForIssuanceDraft(issuanceId, page, pageSize)
            ?: throw NotFoundException("Issuance draft not found")
        return PagedDraftMarketingAssets(
            page = response.page,
            count = response.count,
            marketingAssets = response.data.map { it.marketingAsset.toSummary(it.downloadLink) }
        )
    }
}

fun DraftMarketingAssetDetailDto.toResponse(): IssuanceDraftMarketingAssetDetailResponse {
    return IssuanceDraftMarketingAssetDetailResponse(
        id.id,
        createdAt,
        creatorId.id,
        name,
        dmsPath,
        fileType,
        issuanceDraftId.id
    )
}

fun DraftMarketingAsset.toSummary(downloadLink: URI?): IssuanceDraftMarketingAssetSummaryResponse = IssuanceDraftMarketingAssetSummaryResponse(
    id.id,
    createdAt,
    creatorId.id,
    name,
    type,
    downloadLink,
)