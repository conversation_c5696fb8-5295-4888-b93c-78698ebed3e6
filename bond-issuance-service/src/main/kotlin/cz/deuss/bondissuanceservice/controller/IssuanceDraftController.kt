package cz.deuss.bondissuanceservice.controller

import cz.deuss.bondissuanceservice.api.IssuanceDraftApi
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftCreate
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftResponse
import cz.deuss.bondissuanceservice.api.model.IssuanceDraftUpdate
import cz.deuss.bondissuanceservice.api.model.PagedIssuanceDraftsResponse
import cz.deuss.bondissuanceservice.data.convertor.toApi
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraft
import cz.deuss.bondissuanceservice.facade.authorization.IssuanceDraftAuthorizationFacade
import cz.deuss.bondissuanceservice.facade.authorization.IssuanceDraftError
import cz.deuss.bondissuanceservice.facade.authorization.IssuanceUpdateError
import cz.deuss.bondissuanceservice.service.IssuanceDraftService
import cz.deuss.bondissuanceservice.validation.IssuanceDraftValidationResult
import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.platform.offchain.framework.exceptions.ConflictException
import cz.deuss.platform.offchain.framework.exceptions.ForbiddenException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import io.micronaut.http.HttpResponse
import io.micronaut.openapi.visitor.security.SecurityRule
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import java.util.UUID
import javax.naming.ServiceUnavailableException

@ExposedController
@Secured(SecurityRule.IS_AUTHENTICATED)
@ExecuteOn(TaskExecutors.BLOCKING)
open class IssuanceDraftController(
    private val issuanceDraftService: IssuanceDraftService,
    private val issuanceDraftAuthorizationFacade: IssuanceDraftAuthorizationFacade,
) : IssuanceDraftApi {

    override fun createIssuanceDraft(issuanceDraftCreate: IssuanceDraftCreate): HttpResponse<IssuanceDraftResponse> {
        val result = issuanceDraftAuthorizationFacade.createIssuanceDraft(issuanceDraftCreate)
            .validOrThrow(::handleGeneralError)
        return HttpResponse.created(result.toResponse())
    }

    override fun getIssuanceDraft(issuanceId: UUID): IssuanceDraftResponse {
        val validResult = issuanceDraftAuthorizationFacade.getIssuanceDraft(issuanceId).validOrThrow { error ->
            handleGeneralError(error)
        }
        return validResult.toResponse()
    }

    override fun updateIssuanceDraft(
        issuanceId: UUID,
        issuanceDraftUpdate: IssuanceDraftUpdate,
    ): IssuanceDraftResponse {
        val result = issuanceDraftAuthorizationFacade.updateIssuanceDraft(issuanceId, issuanceDraftUpdate)
        val validResult = result.validOrThrow { error ->
            when (error) {
                is IssuanceDraftError -> handleGeneralError(error)
                is IssuanceUpdateError.ValidationError -> {
                    when (error.validationError) {
                        is IssuanceDraftValidationResult.Failure.IssuanceDraftDoesNotExist -> NotFoundException(error.validationError.message)
                        is IssuanceDraftValidationResult.Failure.InvalidIsinCountry -> ConflictException(error.validationError.message)
                        is IssuanceDraftValidationResult.Failure.InconsistentIsinAndCountry -> ConflictException(error.validationError.message)
                    }
                }
            }
        }
        return validResult.toResponse()
    }


    override fun deleteIssuanceDraft(issuanceId: UUID): HttpResponse<Void> {
        issuanceDraftAuthorizationFacade.deleteIssuanceDraft(issuanceId).validOrThrow { error ->
            handleGeneralError(error)
        }
        return HttpResponse.noContent()
    }

    @Secured(Role.ADMIN)
    override fun getIssuanceDrafts(page: Int, pageSize: Int): PagedIssuanceDraftsResponse {
        logger.action("adminGetIssuanceDrafts")
            .started()
            .param("page", page)
            .param("pageSize", pageSize)
            .info()
        return issuanceDraftService.getIssuanceDrafts(page, pageSize).also {
            logger.action("adminGetIssuanceDrafts").finished().info()
        }
    }

    override fun getMyIssuanceDrafts(page: Int, pageSize: Int, companyId: UUID?): PagedIssuanceDraftsResponse {
        logger.action("getMyIssuanceDrafts")
            .started()
            .param("page", page)
            .param("pageSize", pageSize)
            .info()
        val result = if (companyId != null) {
            issuanceDraftAuthorizationFacade.getIssuanceDraftsForEmployeeAndCompany(
                page = page,
                pageSize = pageSize,
                companyId = companyId
            ).validOrThrow { error ->
                handleGeneralError(error).also {
                    logger.action("getMyIssuanceDrafts").failed().param("reason", it.message).info()
                }
            }
        } else {
            issuanceDraftAuthorizationFacade.getIssuanceDraftsForEmployee(page, pageSize).validOrThrow { error ->
                handleGeneralError(error).also {
                    logger.action("getMyIssuanceDrafts").failed().param("reason", it.message).info()
                }
            }
        }
        return result.also {
            logger.action("getMyIssuanceDrafts").finished().info()
        }

    }

    private companion object {
        private val logger = DeussLogger.semanticLogger(IssuanceDraftController::class)
    }

    private fun handleGeneralError(issuanceDraftError: IssuanceDraftError): Exception {
        return when (issuanceDraftError) {
            is IssuanceDraftError.Forbidden -> ForbiddenException(issuanceDraftError.message)
            is IssuanceDraftError.Unrecoverable -> ServiceUnavailableException("Service is temporary unavailable")
        }
    }
}

fun IssuanceDraft.toResponse(): IssuanceDraftResponse = IssuanceDraftResponse(
    id = this.id,
    name = this.name,
    description = this.description,
    state = this.state.toApi(),
    created = this.created,
    companyId = this.issuer.id,
    industryCode = this.industryCode?.toApi(),
    issueDate = this.issueDate,
    interestPaymentType = this.interestPaymentType?.toApi(),
    bondNominalValue = this.bondNominalValue,
    bondCount = this.bondCount,
    interest = this.interest,
    maturity = this.maturity,
    country = this.country.toApi(),
    currency = this.currency?.toApi(),
    scoring = this.scoring?.toApi(),
    ISIN = this.isin,
    lastEdit = this.lastEdit,
    volume = this.totalBondMoneyVolume
)
