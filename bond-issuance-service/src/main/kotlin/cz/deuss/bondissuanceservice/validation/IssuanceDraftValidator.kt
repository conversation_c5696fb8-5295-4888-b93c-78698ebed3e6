package cz.deuss.bondissuanceservice.validation

import cz.deuss.bondissuanceservice.database.repository.IssuanceDraftRepository
import cz.deuss.bondissuanceservice.domain.issuance.CountryCode
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraft
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraftId
import jakarta.inject.Singleton
import java.util.UUID
import org.apache.commons.validator.routines.ISINValidator


@Singleton
class IssuanceDraftValidator(
    private val issuanceDraftRepository: IssuanceDraftRepository,
) {
    fun getIssuanceDraftExistence(issuanceDraftId: UUID): IssuanceDraftId? {
        if (issuanceDraftRepository.existsById(issuanceDraftId)) {
            return IssuanceDraftId(issuanceDraftId)
        }
        return null
    }
}

sealed interface IssuanceDraftValidationResult<out DRAFT : Any> {
    data class Success<DRAFT : Any>(val draft: DRAFT) : IssuanceDraftValidationResult<DRAFT>

    sealed class Failure(val message: String) : IssuanceDraftValidationResult<Nothing> {
        class InconsistentIsinAndCountry(message: String) : Failure(message)
        class InvalidIsinCountry(message: String) : Failure(message)
        class IssuanceDraftDoesNotExist(message: String) : Failure(message)
    }

}