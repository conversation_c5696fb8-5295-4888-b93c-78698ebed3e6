import io.micronaut.gradle.openapi.tasks.OpenApiClientGenerator
import io.micronaut.gradle.openapi.tasks.OpenApiServerGenerator

private val javaVersion: String by project

version = "0.0.0"
group = "cz.deuss"

plugins {
    id(libs.plugins.kotlin.noarg.get().pluginId)
    id(libs.plugins.kotlin.allOpen.get().pluginId)
    id(libs.plugins.kotlin.jvm.get().pluginId)
    id(libs.plugins.google.ksp.get().pluginId)
    id(libs.plugins.gradle.shadow.get().pluginId)
    id(libs.plugins.micronaut.application.get().pluginId)
    id(libs.plugins.micronaut.openapi.get().pluginId)
    alias(libs.plugins.apollogql.codegen)
}


dependencies {
    ksp("io.micronaut.data:micronaut-data-processor")
    ksp("io.micronaut:micronaut-http-validation")
    ksp("io.micronaut.serde:micronaut-serde-processor")
    ksp("io.micronaut.validation:micronaut-validation-processor")
    ksp("io.micronaut.openapi:micronaut-openapi")
    implementation(project(":bond-issuance-service:domain"))
    implementation(platform(libs.micronaut.platform))
    implementation(libs.bundles.micronaut.validation)
    implementation(libs.bundles.micronaut.data)
    implementation(libs.bundles.micronaut.security)
    implementation(project(":deuss-platform-offchain-framework:framework"))
    implementation(libs.bundles.ethers)
    implementation(project(":bond-issuance-service:blockchain"))
    implementation(project(":shared:deuss-common"))
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime")
    implementation("io.micronaut.serde:micronaut-serde-jackson")
    implementation("io.micronaut:micronaut-management")
    implementation(libs.bundles.kotlin)
    implementation(libs.kotlin.logging)
    implementation(libs.apache.commons.validator)
    implementation("io.micronaut:micronaut-http-client")
    implementation("io.micronaut:micronaut-retry")
    implementation(libs.apollogql.runtime)
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.2")
    runtimeOnly("ch.qos.logback:logback-classic")
    runtimeOnly("com.fasterxml.jackson.module:jackson-module-kotlin")
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")
    runtimeOnly("org.yaml:snakeyaml")
    testImplementation(platform(libs.test.containers.bom))
    testImplementation(libs.bundles.test.base)
    testImplementation(libs.bundles.test.containers)
    testImplementation(project(":deuss-platform-offchain-framework:test"))
    testImplementation(libs.io.mockk)
    testImplementation(libs.mockwebserver)
}

application {
    mainClass = "cz.deuss.bondissuanceservice.ApplicationKt"
}
java {
    sourceCompatibility = JavaVersion.toVersion(javaVersion)
}


graalvmNative.toolchainDetection = false

micronaut {
    version = libs.versions.micronaut.version.get()
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("cz.deuss.bondservice.*")
    }
    openapi {
        val specsSource = project(":api-specifications").layout.buildDirectory

        server(specsSource.get().dir("bond-issuance-service").file("full.yaml").asFile) {
            apiPackageName = "cz.deuss.bondissuanceservice.api"
            modelPackageName = "cz.deuss.bondissuanceservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
        }
        client(
            "company-service-client-internal",
            specsSource.get().dir("company-service").file("internal.yaml").asFile
        ) {
            // TBH unsure if this is a good idea to use relative path to file inside company service, but will try it
            apiPackageName = "cz.deuss.companyservice.internal.api"
            modelPackageName = "cz.deuss.companyservice.internal.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
            clientId = "company-service"
            sortParamsByRequiredFlag = true
        }

        client("file-service", specsSource.get().dir("file-service").file("internal.yaml").asFile) {
            apiPackageName = "cz.deuss.fileservice.api"
            modelPackageName = "cz.deuss.fileservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
            clientId = "file-service"
            sortParamsByRequiredFlag = true
        }
    }
}

noArg {
    annotations("jakarta.persistence.Entity", "jakarta.persistence.MappedSuperclass")
}

allOpen {
    annotations("jakarta.persistence.Entity", "jakarta.persistence.MappedSuperclass")
}

apollo {
    service("indexer") {
        packageName = "cz.deuss.bondissuanceservice.indexer"
        failOnWarnings = true
        srcDir("src/main/resources/indexer")
        includes.add("queries/*.graphql")
        introspection {
            endpointUrl = "http://**************:8350/graphql"
            schemaFile.set(file("src/main/resources/indexer/schema.graphqls"))
        }
        warnOnDeprecatedUsages = true
        failOnWarnings = true
        generateOptionalOperationVariables = false
        mapScalar("BigInt", "java.math.BigInteger", "cz.deuss.bondissuanceservice.client.indexer.adapter.BigIntAdapter")
        mapScalar(
            "DateTime",
            "java.time.LocalDateTime",
            "cz.deuss.bondissuanceservice.client.indexer.adapter.DateTimeAdapter"
        )
    }
}

tasks {
    named<io.micronaut.gradle.docker.NativeImageDockerfile>("dockerfileNative") {
        jdkVersion = javaVersion
    }

    // re-build specifications before codegen
    withType<OpenApiServerGenerator>() {
        // Make sure Gradle knows to track this input and run tasks in order
        dependsOn(":api-specifications:build")
    }
    // re-build specifications before codegen
    withType<OpenApiClientGenerator>() {
        // Make sure Gradle knows to track this input and run tasks in order
        dependsOn(":api-specifications:build")
    }

    test {
        testLogging {
            showStandardStreams = true
            showStackTraces = true
            showCauses = true
            showExceptions = true
            events("passed", "skipped", "failed")
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        }
        useJUnitPlatform {
            includeEngines("junit-jupiter", "kotest")
        }
        reports {
            junitXml.outputLocation = file("${project.layout.buildDirectory.get()}/reports/tests/test/xml")
        }
    }
}
