plugins {
    kotlin("jvm")
    alias(libs.plugins.ethers.abigen)
    alias(libs.plugins.kotlin.noarg)
    alias(libs.plugins.kotlin.allOpen)
    id("io.micronaut.library")
    alias(libs.plugins.google.ksp)
}

dependencies {
    implementation("io.micronaut:micronaut-inject")
    implementation(libs.kotlin.logging)
    implementation(project(":deuss-platform-offchain-framework:framework"))
    implementation(project(":deuss-platform-offchain-framework:chain"))
    implementation(libs.bundles.ethers)
    implementation(project(":bond-issuance-service:domain"))
}

micronaut {
    version = libs.versions.micronaut.version.get()
    processing {
        incremental(true)
        annotations("cz.deuss.bondissuanceservice.blockchain.provider**")
    }
}


ethersAbigen {
    directorySource("src/main/resources/abi")
    outputDir = "ethers/generated/source"
}
