package cz.deuss.bondissuanceservice.blockchain.data

import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId


fun LocalDateTime.toEpochSecond(): BigInteger {
    val epochSeconds = this.atZone(ZoneId.systemDefault()).toEpochSecond()
    return BigInteger.valueOf(epochSeconds)
}

fun LocalDate.toEpochSecond(): BigInteger {
    return this.atStartOfDay().toEpochSecond()
}