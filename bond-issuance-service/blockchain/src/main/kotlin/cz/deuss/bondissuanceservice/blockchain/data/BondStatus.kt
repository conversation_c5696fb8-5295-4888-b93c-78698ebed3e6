package cz.deuss.bondissuanceservice.blockchain.data

import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceState
import java.math.BigInteger

enum class BondStatus(val chainValue: BigInteger) {
    UNREGISTERED(BigInteger.ZERO),
    PENDING(BigInteger.ONE),
    ISSUED(BigInteger.TWO),
    REDEEMED(BigInteger.valueOf(3)),
    SUSPENDED(BigInteger.valueOf(4));


    companion object {
        fun fromIssuanceState(issuanceState: IssuanceState): BondStatus {
            return when (issuanceState) {
                IssuanceState.DRAFT -> PENDING
                IssuanceState.WAITING_FOR_REVIEW -> PENDING
                IssuanceState.IN_REVIEW -> PENDING
                IssuanceState.APPROVED -> PENDING
                IssuanceState.CHANGES_REQUESTED -> PENDING
                IssuanceState.PUBLISHED -> ISSUED
            }
        }
    }
}