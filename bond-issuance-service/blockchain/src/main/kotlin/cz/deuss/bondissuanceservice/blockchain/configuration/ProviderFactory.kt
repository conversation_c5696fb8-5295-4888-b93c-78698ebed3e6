package cz.deuss.bondissuanceservice.blockchain.configuration

import cz.deuss.platform.offchain.framework.logging.DeussLogger
import io.ethers.core.Result
import io.ethers.providers.Provider
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Value

@Factory
class ProviderFactory {

    @Bean
    fun getProvider(
        @Value($$"${deuss.blockchain.url}") url: String,
    ): Provider {
        val provider = Provider.fromUrl(url)
        when (provider) {
            is Result.Success -> return provider.value.also {
                logger.message("Successfully connected to chain at url: $url").info()
            }

            is Result.Failure -> {
                when (provider.error) {
                    is Provider.UnsupportedUrlProtocol -> {
                        logger
                            .message("Not supported protocol for provider ${provider.error}")
                            .error()
                    }

                    is Provider.UnableToGetChainId -> {
                        logger
                            .message("Unable to get chain id, probably unable to connect to chain ${provider.error}")
                            .error()
                    }
                }
                error("Unable to connect to chain with cause ${provider.error}")
            }
        }
    }


    companion object {
        private val logger = DeussLogger.semanticLogger(ProviderFactory::class)
    }

}