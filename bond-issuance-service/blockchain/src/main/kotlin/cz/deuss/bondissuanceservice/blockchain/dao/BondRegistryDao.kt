package cz.deuss.bondissuanceservice.blockchain.dao

import cz.deuss.bondissuanceservice.domain.issuance.Isin
import cz.deuss.bondissuanceservice.domain.issuance.IssuanceDraftValidationVerdict
import cz.deuss.contract.bond.Errors
import cz.deuss.contract.bond.IBondRegistryV2
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import io.ethers.core.Result
import io.ethers.core.types.Address
import io.ethers.core.types.Bytes
import io.ethers.core.types.Hash
import io.ethers.providers.Provider
import io.ethers.signers.PrivateKeySigner
import io.ethers.signers.Signer
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton

@Singleton
open class BondRegistryDao(
    @Value($$"${deuss.blockchain.addresses.bond-registry}")
    bondRegistryAddress: String,
    @Value($$"${deuss.blockchain.private-keys.bond-issuance-service}")
    privateKey: String,
    private val provider: Provider,
) {

    private val bondRegistry: IBondRegistryV2 = IBondRegistryV2(provider = provider, Address(bondRegistryAddress))
    private val bondIssuanceSigner: Signer = PrivateKeySigner(privateKey)


    fun postIssuanceDraftValidationResult(verdict: IssuanceDraftValidationVerdict): ValidationResultResponse {
        val response = bondRegistry
            .handleRegistrationProposal(verdict.isin.value, verdict.isValid)
            .send(bondIssuanceSigner)
            .sendAwait()
        return when (response) {
            is Result.Success -> {
                handleChainResponse(response.value.hash, isin = verdict.isin)
            }

            is Result.Failure<Result.Error> -> {
                logger.message("Unable to publish validation verdict to chain")
                    .param("error", response.error.toString())
                    .param("isin", verdict.isin.value)
                    .error()
                ValidationResultResponse.Failure
            }
        }
    }

    private fun handleChainResponse(hash: Hash, isin: Isin): ValidationResultResponse {
        val error = Errors.decodeError(Bytes(hash.asByteArray())) ?: return ValidationResultResponse.Success
        when (error) {
            is Errors.BondRegistry__NonExistentProposal -> {
                logger.message("Unable to validate issuance draft proposal on blockchain. It does not exists.")
                    .param("isin", isin.value)
                    .warn()
            }

            is Errors.BondRegistry__BondDeploymentFailed -> {
                logger.message("Unable to deploy issuance draft proposal on blockchain.")
                    .param("isin", isin.value)
                    .warn()
            }

            is Errors.BondRegistry__BondAddressZero -> {
                logger.message("Unable to deploy issuance draft proposal on blockchain. Bond address is zero.")
                    .param("isin", isin.value)
                    .warn()
            }

            else -> {
                logger.message("Unexpected error from chain occurred.")
                    .param("isin", isin.value)
                    .param("error", error.toString())
                    .error()
            }
        }
        return ValidationResultResponse.Failure
    }

    companion object {
        private val logger = DeussLogger.semanticLogger(BondRegistryDao::class)
    }
}

sealed interface ValidationResultResponse {
    data object Success : ValidationResultResponse
    data object Failure : ValidationResultResponse
}




