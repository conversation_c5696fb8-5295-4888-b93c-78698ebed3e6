package cz.deuss.bondissuanceservice.domain.company

import java.util.UUID

data class Company(
    val id: UUID,
    val name: String,
)

data class CompanyId(
    val id: UUID,
)

data class FullCompany(
    val id: UUID,
    val name: String,
    val verificationState: CompanyVerificationStatus,
) {
    fun toCompany(): Company = Company(this.id, this.name)
}

data class CompanyWithUserRole(
    val company: FullCompany,
    val employeeRole: EmployeeRole,
)