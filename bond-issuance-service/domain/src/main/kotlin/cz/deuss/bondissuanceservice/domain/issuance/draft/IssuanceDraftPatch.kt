package cz.deuss.bondissuanceservice.domain.issuance.draft

import cz.deuss.bondissuanceservice.domain.issuance.Currency
import cz.deuss.bondissuanceservice.domain.issuance.IndustryCode
import cz.deuss.bondissuanceservice.domain.issuance.InterestPaymentRate
import java.math.BigDecimal
import java.time.LocalDate


data class IssuanceDraftPatch(
    val maturity: LocalDate?,
    val description: String?,
    val currency: Currency?,
    val bondNominalValue: BigDecimal?,
    val bondCount: Int?,
    val interest: BigDecimal?,
    val issueDate: LocalDate?,
    val interestPaymentType: InterestPaymentRate?,
    val name: String?,
    val isin: String?,
    val industryCode: IndustryCode?,
    val scoring: Scoring?,
)