package cz.deuss.bondissuanceservice.domain.issuance.draft.document

import DraftDocumentId
import cz.deuss.bondissuanceservice.domain.issuance.draft.IssuanceDraftId
import cz.deuss.bondissuanceservice.domain.user.UserId
import java.time.LocalDateTime

data class DraftDocument(
    val id: DraftDocumentId,
    val name: String,
    val type: DocumentFileType,
    val creatorId: UserId,
    val issuanceDraftId: IssuanceDraftId,
    val dmsPath: String,
    val createdAt: LocalDateTime,
) {
    val fullFilename: String
        get() = "$name.${type.name.lowercase()}"
}