package cz.deuss.bondissuanceservice.domain.issuance.draft

import cz.deuss.bondissuanceservice.domain.company.Company
import cz.deuss.bondissuanceservice.domain.issuance.CountryCode
import cz.deuss.bondissuanceservice.domain.issuance.Currency
import cz.deuss.bondissuanceservice.domain.issuance.IndustryCode
import cz.deuss.bondissuanceservice.domain.issuance.InterestPaymentRate
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class IssuanceDraft(
    val id: UUID,
    val state: IssuanceState,
    val scoring: Scoring?,
    val name: String,
    val description: String?,
    val isin: String?,
    val country: CountryCode,
    val bondCount: Int?,
    val bondNominalValue: BigDecimal?,
    val interest: BigDecimal?,
    val issueDate: LocalDate?,
    val currency: Currency?,
    val interestPaymentType: InterestPaymentRate?,
    val maturity: LocalDate?,
    val issuer: Company,
    val industryCode: IndustryCode?,
    val created: LocalDateTime,
    val lastEdit: LocalDateTime,
    val totalBondMoneyVolume: BigDecimal?,
)






