[versions]
micronaut-plugin-version = "4.5.4"
kotlin-version = "2.2.0"
micronaut-version = "4.9.1"
kotlin-logging-version = "7.0.4"
ksp-plugin-version = "2.2.0-2.0.2"
shadow-plugin-version = "8.3.5"

test-containers-version = "1.20.4"
assertj-version = "3.27.2"
mockk-version = "1.13.17"
kotest-version = "5.9.1"

logback-encoder = "8.1"
logback-classic-version = "1.5.18"
mockwebserver-version = "5.1.0"
apache-commons-validator-version = "1.9.0"

# chain dependencies
ethers-version = "1.3.2"
web3j-version = "4.14.0"
# codegen dependencies
javaPoet-version = "1.13.0"
picocli-version = "4.7.6"

apollo-version = "4.3.1"

[plugins]
micronaut-application = { id = "io.micronaut.application", version.ref = "micronaut-plugin-version" }
micronaut-openapi = { id = "io.micronaut.openapi", version.ref = "micronaut-plugin-version" }
micronaut-library = { id = "io.micronaut.library", version.ref = "micronaut-plugin-version" }
micronaut-docker = { id = "io.micronaut.docker", version.ref = "micronaut-plugin-version" }
micronaut-aot = { id = "io.micronaut.aot", version.ref = "micronaut-plugin-version" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin-version" }
kotlin-noarg = { id = "org.jetbrains.kotlin.plugin.noarg", version.ref = "kotlin-version" }
kotlin-allOpen = { id = "org.jetbrains.kotlin.plugin.allopen", version.ref = "kotlin-version" }
google-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp-plugin-version" }
gradle-shadow = { id = "com.gradleup.shadow", version.ref = "shadow-plugin-version" }
apollogql-codegen = { id = "com.apollographql.apollo", version.ref = "apollo-version" }
ethers-abigen = { id = "io.kriptal.ethers.abigen-plugin", version.ref = "ethers-version" }

[libraries]

micronaut-platform = { module = "io.micronaut.platform:micronaut-platform", version.ref = "micronaut-version" }


# Yubico WebAuthn libraries
yubicoWebauthnCore = { module = "com.yubico:webauthn-server-core", version = "2.6.0" }
yubicoWebauthnAttestation = { module = "com.yubico:webauthn-server-attestation", version = "2.6.0" }

# Spring Security libraries
springSecurityCrypto = { module = "org.springframework.security:spring-security-crypto", version = "6.3.0" }
commonLogging = { module = "commons-logging:commons-logging", version = "1.2" }
bouncyCastle = { module = "org.bouncycastle:bcpkix-jdk18on", version = "1.81" }

micronaut-data-jpa = { module = "io.micronaut.data:micronaut-data-hibernate-jpa" }
micronaut-data-hibernate = { module = "io.micronaut.data:micronaut-data-tx-hibernate" }
micronaut-data-hikari = { module = "io.micronaut.sql:micronaut-jdbc-hikari" }
micronaut-data-runtime = { module = "io.micronaut.data:micronaut-data-runtime" }
micronaut-data-flyway = { module = "io.micronaut.flyway:micronaut-flyway" }

micronaut-kotlin = { module = "io.micronaut.kotlin:micronaut-kotlin-runtime" }
micronaut-serde = { module = "io.micronaut.serde:micronaut-serde-jackson" }
micronaut-validation = { module = "io.micronaut.validation:micronaut-validation" }
micronaut-validator = { module = "io.micronaut.beanvalidation:micronaut-hibernate-validator" }

micronaut-security = { module = "io.micronaut.security:micronaut-security" }
micronaut-security-jwt = { module = "io.micronaut.security:micronaut-security-jwt" }
micronaut-security-oauth2 = { module = "io.micronaut.security:micronaut-security-oauth2" }


kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin-version" }
kotlin-jdk8StdLib = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin-version" }
# Logging
kotlin-logging = { module = "io.github.oshai:kotlin-logging-jvm", version.ref = "kotlin-logging-version" }
logback-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logback-encoder" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback-classic-version" }
slf4j-api = { module = "org.slf4j:slf4j-api" }

test-containers-bom = { module = "org.testcontainers:testcontainers-bom", version.ref = "test-containers-version" }
test-constainers-core = { module = "org.testcontainers:testcontainers", version.ref = "test-containers-version" }
test-containers-postgres = { module = "org.testcontainers:postgresql", version.ref = "test-containers-version" }

# chain libraries
web3j = { module = "org.web3j:core", version.ref = "web3j-version" }
etherskt-bom = { group = "io.kriptal.ethers", name = "ethers-bom", version.ref = "ethers-version" }
ethers-abi = { group = "io.kriptal.ethers", name = "ethers-abi", version.ref = "ethers-version" }
ethers-core = { group = "io.kriptal.ethers", name = "ethers-core", version.ref = "ethers-version" }
ethers-providers = { group = "io.kriptal.ethers", name = "ethers-providers", version.ref = "ethers-version" }
ethers-signers = { group = "io.kriptal.ethers", name = "ethers-signers", version.ref = "ethers-version" }

# Code generation dependencies for Web3j
web3j-codegen = { module = "org.web3j:codegen", version.ref = "web3j-version" }
picocli = { module = "info.picocli:picocli", version.ref = "picocli-version" }
javapoet = { module = "com.squareup:javapoet", version.ref = "javaPoet-version" }

apache-commons-validator = { group = "commons-validator", name = "commons-validator", version.ref = "apache-commons-validator-version" }

apollogql-runtime = { group = "com.apollographql.apollo", name = "apollo-runtime", version.ref = "apollo-version" }

# Testing libraries
assertj-bom = { module = "org.assertj:assertj-bom", version.ref = "assertj-version" }
assertj-core = { module = "org.assertj:assertj-core", version.ref = "assertj-version" }

junit-jupiter = { module = "org.junit.jupiter:junit-jupiter" }

io-mockk = { module = "io.mockk:mockk", version.ref = "mockk-version" }

kotest-runner = { module = "io.kotest:kotest-runner-junit5", version.ref = "kotest-version" }
kotest-assertion = { module = "io.kotest:kotest-assertions-core", version.ref = "kotest-version" }
mockwebserver = { module = "com.squareup.okhttp3:mockwebserver", version.ref = "mockwebserver-version" }

[bundles]
micronaut-data = ["micronaut-data-jpa", "micronaut-data-hibernate", "micronaut-data-hikari", "micronaut-data-runtime", "micronaut-data-flyway"]
micronaut-validation = ["micronaut-validation", "micronaut-validator"]
micronaut-security = ["micronaut-security", "micronaut-security-jwt", "micronaut-security-oauth2"]
kotlin = ["kotlin-reflect", "kotlin-jdk8StdLib"]
test-base = ["assertj-bom", "assertj-core", "junit-jupiter"]
test-containers = ["test-constainers-core", "test-containers-postgres"]
test-kotest = ["kotest-runner", "kotest-assertion"]
logging = ["kotlin-logging", "logback-encoder"]
web3jCodegen = ["web3j-codegen", "picocli", "javapoet"]
ethers = ["ethers-abi", "ethers-core", "ethers-providers", "ethers-signers"]
