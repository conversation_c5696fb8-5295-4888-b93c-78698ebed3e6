package cz.duess.common.logging

import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.classic.spi.ThrowableProxyUtil
import ch.qos.logback.core.encoder.EncoderBase
import java.nio.charset.StandardCharsets
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * Intended for local development.
 * From Structured logging build unstructured  log line - readable for humans.
 */
class DeussHumanFriendlyLayoutEncoder : EncoderBase<ILoggingEvent?>() {
    override fun headerBytes(): ByteArray? {
        // We don't need a header.
        return null
    }

    /**
     * This method is called for each log event. It builds the final log line.
     */
    override fun encode(event: ILoggingEvent?): ByteArray {
        if (event == null) {
            return byteArrayOf()
        }

        val encoded = buildString(128) {
            // 1. Append standard log fields in a readable format.
            append(FORMATTER.format(Instant.ofEpochMilli(event.timeStamp)))
            append(" [").append(event.threadName).append("] ")
            append(String.format("%-5s", event.level.toString()))
            append(" ").append(event.loggerName).append(" - ")

            if (event.formattedMessage != null) {
                append(event.formattedMessage)
            }

            if (event.keyValuePairs.isNotEmpty()) {
                val encodedPairs = event.keyValuePairs.joinToString { pair -> "${pair.key}=${pair.value}" }
                append(encodedPairs)
                append(" ")
            }

            // 2. Append dynamic fields from the MDC (Mapped Diagnostic Context).
            val mdcProperties = event.mdcPropertyMap
            if (mdcProperties != null && mdcProperties.isNotEmpty()) {
                val mdcString = mdcProperties.entries.joinToString { entry -> "${entry.key}='${entry.value}'" }
                append(mdcString)
                append(" ")
            }

            // 3. Append a line separator.
            append(System.lineSeparator())

            // 4. Append stack trace if an exception exists.
            val throwableProxy = event.throwableProxy
            if (throwableProxy != null) {
                // Use Logback's utility to format the stack trace.
                val stackTrace = ThrowableProxyUtil.asString(throwableProxy)
                append(stackTrace)
                append(System.lineSeparator())
            }
        }

        return encoded.toByteArray(StandardCharsets.UTF_8)
    }

    override fun footerBytes(): ByteArray? {
        // We don't need a footer.
        return null
    }

    companion object {
        private val FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss.SSS").withZone(ZoneId.systemDefault())
    }
}
