package cz.deuss

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.cbor.databind.CBORMapper
import com.yubico.webauthn.data.COSEAlgorithmIdentifier
import cz.deuss.platform.offchain.chain.DeussChain
import cz.deuss.userservice.api.model.FinishPasskeyRegistrationInput
import cz.deuss.userservice.api.model.FinishUserRegistrationRequest
import cz.deuss.userservice.api.model.RegistrationOptionsPubKeyCredParamsInner
import cz.deuss.userservice.api.model.RegistrationStartRequest
import cz.deuss.userservice.api.model.UserData
import cz.deuss.userservice.api.model.UserRegistrationData
import cz.deuss.userservice.api.model.UserRole
import cz.deuss.userservice.registration.UserPasskeyRegistrationController
import cz.deuss.userservice.service.UserService
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldHaveAtMostSize
import io.kotest.matchers.date.shouldBeBefore
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.annotation.Value
import io.micronaut.http.HttpStatus
import jakarta.inject.Inject
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import java.math.BigInteger
import java.security.KeyPairGenerator
import java.security.SecureRandom
import java.security.Signature
import java.security.interfaces.ECPrivateKey
import java.security.interfaces.ECPublicKey
import java.security.spec.ECGenParameterSpec
import java.time.LocalDateTime
import java.util.*

class RegistrationFlowTest : DeussUserServiceTest() {

    @Inject
    private lateinit var controller: UserPasskeyRegistrationController

    @Value($$"${passkey.relying-parties.test.origin}")
    private lateinit var origin: String

    @Inject
    private lateinit var objectMapper: ObjectMapper

    @Inject
    private lateinit var testUserInvitationHelper: TestUserInvitationHelper

    @Inject
    private lateinit var userService: UserService

    @Inject
    private lateinit var deussChain: DeussChain

    private val cborMapper = CBORMapper()

    @Test
    fun registrationFlow() {
        val invitationToken = testUserInvitationHelper.generateInvitationToken()
        val userEmail = generateEmail()
        val options = controller.startUserRegistration(RegistrationStartRequest(userEmail, invitationToken))
        // for other formats different attestation body is needed so it require redo test.
        options.attestation shouldBe "none"

        val userData = UserRegistrationData(
            firstName = "John",
            lastName = "Doe",
        )

        val finishRegistrationReq = FinishUserRegistrationRequest(
            passkey = FinishPasskeyRegistrationInput(
                session = options.session,
                attestationResponse = PasskeyAttestationBuilder(cborMapper, objectMapper, options.rp.id).build(
                    clientData = buildClientData(options.challenge),
                    attestationFormat = options.attestation,
                    signer = checkAndGenerateSigner(options.pubKeyCredParams)
                ),
            ),
            userData = userData,
        )

        val registrationFinished = controller.finishUserRegistration(finishRegistrationReq)
        registrationFinished.status shouldBe HttpStatus.CREATED
        val createdUser = registrationFinished.body.get()
        createdUser.email shouldBe userEmail

        val registeredUser = userService.getUserById(createdUser.id)
        assertUserData(registeredUser, userEmail, userData)
        assertUserHasOnchainAccount(registeredUser)
    }

    private fun assertUserData(actual: UserData, expectedEmail: String, expected: UserRegistrationData) {
        actual.email shouldBe expectedEmail
        actual.firstName shouldBe expected.firstName
        actual.lastName shouldBe expected.lastName
        actual.createdAt shouldBeBefore LocalDateTime.now()

        actual.roles shouldContainExactly setOf(UserRole.USER)
    }

    @OptIn(ExperimentalStdlibApi::class)
    private fun assertUserHasOnchainAccount(userData: UserData) {
        val accounts = userService.getUserOnchainAccountsById(userData.id)
        accounts shouldHaveAtMostSize 1

        val passkeyIdBytes = Base64.getDecoder().decode(accounts[0].passkeyId).toHexString()
        val passkeyId = BigInteger(passkeyIdBytes, 16)

        val onchainAccount = deussChain.getUserAccount(passkeyId)

        onchainAccount shouldNotBe null
        onchainAccount!!.id shouldBe passkeyId
    }

    private fun buildClientData(challenge: String): ClientData {
        val clientData = ClientData(
            type = "webauthn.create",
            // is expected to be base64Url encoded
            challenge = Base64.getUrlEncoder().encodeToString(Base64.getDecoder().decode(challenge)),
            origin = origin,
            crossOrigin = false,
        )

        return clientData
    }

    private fun generateEmail() = "${UUID.randomUUID()}@gmail.com"

    private fun checkAndGenerateSigner(
        allowedCredParams: List<RegistrationOptionsPubKeyCredParamsInner>,
    ): ECSigner {
        val alg = COSEAlgorithmIdentifier.ES256

        val containsExpectedAlg = allowedCredParams.any { it.type == "public-key" && it.alg == alg.id.toBigDecimal() }
        if (!containsExpectedAlg) {
            fail("Expecting ${alg.name} which is not present. Adjust alg options.")
        }

        return generateES256KeyPair(alg)
    }

    private fun generateES256KeyPair(alg: COSEAlgorithmIdentifier): ECSigner {
        val keyGen = KeyPairGenerator.getInstance("EC")
        val ecSpec = ECGenParameterSpec("secp256r1") // NIST P-256
        keyGen.initialize(ecSpec, SecureRandom())

        val keyPair = keyGen.generateKeyPair()

        return ECSigner(
            alg = alg,
            publicKey = keyPair.public as ECPublicKey,
            privateKey = keyPair.private as ECPrivateKey,
            signatureProvider = { Signature.getInstance("SHA256withECDSA") },
        )
    }
}

