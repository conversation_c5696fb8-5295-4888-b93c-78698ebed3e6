package cz.deuss.userservice.controller

import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.platform.offchain.framework.exceptions.BaseHttpException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.api.model.LoginFinishRequest
import cz.deuss.userservice.api.model.UserData
import cz.deuss.userservice.api.model.UserRole
import cz.deuss.userservice.mapping.AccessRefreshTokenMapper
import cz.deuss.userservice.passkey.FinishLoginResult
import cz.deuss.userservice.passkey.PasskeyChallengeValidationError
import cz.deuss.userservice.passkey.PasskeyService
import cz.deuss.userservice.passkey.TestOriginProvider
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Value
import io.micronaut.http.HttpStatus
import io.micronaut.security.token.render.AccessRefreshToken
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class PasskeyControllerTest : FunSpec({

    val passkeyService = mockk<PasskeyService>()
    val testOrigin = "http://localhost:63342";
    val tested = PasskeyController(passkeyService, AccessRefreshTokenMapper(), TestOriginProvider(testOrigin))

    beforeTest { clearMocks(passkeyService) }

    context("finish login") {
        val request = mockk<LoginFinishRequest>()

        test("logged") {
            val userId = UUID.randomUUID()
            val deussUser = UserData(
                id = userId,
                email = "email",
                firstName = "firstName",
                lastName = "lastName",
                roles = setOf(UserRole.USER),
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
            )
            every { passkeyService.finishLogin(request, testOrigin) } returns FinishLoginResult.Logged(
                tokens = AccessRefreshToken("token", "refreshToken", "Bearer", 100),
                user = deussUser,
            )

            val response = tested.finishLogin(request)
            response.tokens.accessToken shouldBe "token"
            response.tokens.refreshToken shouldBe "refreshToken"
            response.tokens.tokenType shouldBe "Bearer"
            response.tokens.expiresIn shouldBe 100
            response.user shouldBe deussUser
        }

        test("passkey not found -> bad request") {
            every { passkeyService.finishLogin(request, testOrigin) } returns FinishLoginResult.PasskeyNotFound

            shouldThrow<BadRequestException> { tested.finishLogin(request) }
        }

        test("user not recognized -> not found") {
            every { passkeyService.finishLogin(request, testOrigin) } returns FinishLoginResult.UserNotRecognized

            shouldThrow<NotFoundException> { tested.finishLogin(request) }
        }

        test("challenge not exist or used -> bad request") {
            val passkeyError = PasskeyChallengeValidationError.NotFoundOrUsed

            every { passkeyService.finishLogin(request, testOrigin) } returns FinishLoginResult.Challenge(passkeyError)

            shouldThrow<BadRequestException> { tested.finishLogin(request) }
        }

        test("challenge expired -> request expired") {
            val passkeyError = PasskeyChallengeValidationError.Expired

            every { passkeyService.finishLogin(request, testOrigin) } returns FinishLoginResult.Challenge(passkeyError)

            shouldThrow<BaseHttpException> { tested.finishLogin(request) }
                .status shouldBe HttpStatus.REQUEST_TIMEOUT
        }
    }
})
