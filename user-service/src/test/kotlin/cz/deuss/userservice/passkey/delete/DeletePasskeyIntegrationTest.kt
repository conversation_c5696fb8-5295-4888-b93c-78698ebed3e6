package cz.deuss.userservice.passkey.delete

import cz.deuss.DeussUserServiceTest
import cz.deuss.userservice.api.model.DeleteUserPasskeyCredentialResponse
import cz.deuss.userservice.database.model.DeussPasskey
import cz.deuss.userservice.database.model.DeussUser
import cz.deuss.userservice.database.repository.PasskeyRepository
import io.kotest.matchers.collections.shouldBeSingleton
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import jakarta.inject.Inject
import org.junit.jupiter.api.Test
import java.util.UUID

class DeletePasskeyIntegrationTest : DeussUserServiceTest() {

    @Inject
    private lateinit var repository: PasskeyRepository

    @Test
    fun `delete user passkey`() {
        val toBeDeleted = createPasskey(defaultAuthenticatedUser)
        val remaining = createPasskey(defaultAuthenticatedUser)

        val request = HttpRequest
            .DELETE<Unit>("/auth/passkey/${toBeDeleted.id}")
            .bearerAuth(defaultAuthenticatedUser)

        val response = exposedClient
            .exchange(request, DeleteUserPasskeyCredentialResponse::class.java)

        response.status shouldBe HttpStatus.OK
        response
            .body()
            .shouldNotBeNull()
            .passkeyCredentials
            .shouldBeSingleton { actualPasskey ->
                actualPasskey.id shouldBe remaining.id
            }

        repository.existsById(toBeDeleted.id) shouldBe false
        repository.existsById(remaining.id) shouldBe true
    }

    private fun createPasskey(user: DeussUser): DeussPasskey {
        return repository.save(
            DeussPasskey(
                user = user,
                credentialId = UUID.randomUUID().toString(),
                attestedCredentialData = byteArrayOf(),
                clientData = null,
                publicKeyCose = byteArrayOf(),
                signatureCounter = 0,
                backupState = false,
                backupEligible = false,
                userFriendlyName = "name",
            )
        )
    }
}
