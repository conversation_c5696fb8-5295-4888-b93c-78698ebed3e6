package cz.deuss.userservice.passkey

import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton

@Singleton
@Replaces(HttpRequestOriginProvider::class)
class TestOriginProvider(
    @param:Value($$"${passkey.relying-parties.test.origin}")
    private val testPasskeyOrigin: String
) : OriginProvider {
    override fun requestOrigin(): String = testPasskeyOrigin
}