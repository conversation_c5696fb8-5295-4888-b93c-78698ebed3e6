package cz.deuss

import cz.deuss.platform.offchain.framework.api.model.HttpError
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.test.BaseApplicationTest
import cz.deuss.platform.offchain.test.assert.HttpErrorAssert
import cz.deuss.userservice.database.model.DeussUser
import cz.deuss.userservice.database.repository.UserRepository
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.service.TokenService
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.EmbeddedApplication
import io.micronaut.test.annotation.Sql
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Inject
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import java.util.UUID

@MicronautTest(environments = ["test"], rebuildContext = true, transactional = false)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Sql("classpath:db/clear.sql", phase = Sql.Phase.AFTER_EACH)
@Sql("classpath:db/init.sql", phase = Sql.Phase.BEFORE_EACH)
open class DeussUserServiceTest : BaseApplicationTest("user-service-db") {

    @Inject
    lateinit var application: EmbeddedApplication<*>

    @Inject
    lateinit var tokenService: TokenService

    @Inject
    lateinit var userRepository: UserRepository

    protected lateinit var defaultAuthenticatedUser: DeussUser

    @BeforeEach
    fun setup() {
        defaultAuthenticatedUser =
            userRepository.getById(USER_ID) ?: throw IllegalStateException("Predefined user not found")
    }

    @Test
    fun testItWorks() {
        Assertions.assertTrue(application.isRunning)
    }


    protected inline fun <reified BODY : Any, reified RESPONSE> callApi(
        path: String,
        body: BODY,
        method: HttpMethod = HttpMethod.POST,
        expectedStatus: HttpStatus = HttpStatus.OK,
        caller: DeussUser? = defaultAuthenticatedUser,
    ): RESPONSE {
        val request = HttpRequest.create<BODY>(method, path).body(body).bearerAuth(caller)
        return executeApiCall(request, expectedStatus)
    }


    protected inline fun <reified RESPONSE> callApi(
        path: String,
        method: HttpMethod = HttpMethod.GET,
        expectedStatus: HttpStatus = HttpStatus.OK,
        caller: DeussUser? = defaultAuthenticatedUser,
    ): RESPONSE {
        val request = HttpRequest.create<Unit>(method, path).bearerAuth(caller)
        return executeApiCall(request, expectedStatus)
    }


    protected inline fun <reified BODY : Any, reified RESPONSE> executeApiCall(
        request: MutableHttpRequest<BODY>,
        expectedStatus: HttpStatus,
    ): RESPONSE {
        val response = assertDoesNotThrow {
            exposedClient.exchange(request, RESPONSE::class.java)
        }
        assertThat(response.status).isEqualTo(expectedStatus)

        return response.body()
    }

    protected fun <BODY : Any> callApiExpectError(
        path: String,
        body: BODY,
        expectedError: HttpError,
        method: HttpMethod = HttpMethod.POST,
        user: DeussUser? = defaultAuthenticatedUser,
    ) {
        val request = HttpRequest.create<BODY>(method, path).body(body).bearerAuth(user)
        executeAndValidateErrorCall(request, expectedError)
    }

    protected fun callApiExpectError(
        path: String,
        expectedError: HttpError,
        method: HttpMethod = HttpMethod.GET,
        user: DeussUser? = defaultAuthenticatedUser,
    ) {
        val request = HttpRequest.create<Unit>(method, path).bearerAuth(user)
        executeAndValidateErrorCall(request, expectedError)
    }

    private fun <BODY : Any> executeAndValidateErrorCall(request: MutableHttpRequest<BODY>, expectedError: HttpError) {
        val e = assertThrows<HttpClientResponseException> {
            exposedClient.exchange(request, HttpError::class.java).body()
        }

        assertThat(e.status.code).isEqualTo(expectedError.status)
        HttpErrorAssert.assertThat(e.extractError()).isEqualTo(expectedError)
    }


    protected fun <BODY : Any> MutableHttpRequest<BODY>.bearerAuth(
        user: DeussUser?,
        roles: Set<Role.Enum> = setOf(Role.Enum.USER)
    ): MutableHttpRequest<BODY> {
        if (user != null) {
            val accessToken = tokenService.generateTokens(UserId(user.id), user.email, roles).accessToken
            return bearerAuth(accessToken)
        }

        return this
    }


    companion object {
        val USER_ID: UUID = UUID.fromString("39d91b5a-7b83-4cff-ab9a-bf1f6f113b4d")
    }
}

