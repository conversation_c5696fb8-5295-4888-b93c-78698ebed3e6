package cz.deuss.userservice.controller

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.platform.offchain.framework.exceptions.BaseHttpException
import cz.deuss.platform.offchain.framework.exceptions.InternalServerErrorException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.userservice.api.PasskeyAuthenticationApi
import cz.deuss.userservice.api.model.FinishLoginResponse
import cz.deuss.userservice.api.model.LoginFinishRequest
import cz.deuss.userservice.api.model.LoginOptions
import cz.deuss.userservice.mapping.AccessRefreshTokenMapper
import cz.deuss.userservice.passkey.FinishLoginResult
import cz.deuss.userservice.passkey.OriginProvider
import cz.deuss.userservice.passkey.PasskeyChallengeValidationError
import cz.deuss.userservice.passkey.PasskeyService
import io.micronaut.http.HttpStatus
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import java.net.URI

@ExposedController
@ExecuteOn(TaskExecutors.BLOCKING)
@Secured(SecurityRule.IS_ANONYMOUS)
open class PasskeyController(
    private val passkeyService: PasskeyService,
    private val accessRefreshTokenMapper: AccessRefreshTokenMapper,
    private val originProvider: OriginProvider
) : PasskeyAuthenticationApi {

    private val logger = DeussLogger.semanticLogger(this::class)

    override fun startLogin(): LoginOptions {
        val origin = originProvider.requestOrigin()

        return try {
            passkeyService.startLogin(origin)
        } catch (e: Exception) {
            logger.error(e) { "An error occurred while starting login" }
            throw InternalServerErrorException("An error occurred while starting login")
        }
    }

    override fun finishLogin(loginFinishRequest: LoginFinishRequest): FinishLoginResponse {
        val origin = originProvider.requestOrigin();
        val result = passkeyService.finishLogin(loginFinishRequest, origin)

        return when (result) {
            is FinishLoginResult.Logged -> mapResponse(result)
            FinishLoginResult.PasskeyNotFound -> throw BadRequestException("Passkey does not exist.")
            FinishLoginResult.UserNotRecognized -> throw NotFoundException("User not recognized.")
            is FinishLoginResult.Challenge -> when (result.error) {
                PasskeyChallengeValidationError.Expired -> throw RequestTimeoutException("Passkey challenge expired.")
                PasskeyChallengeValidationError.NotFoundOrUsed -> throw BadRequestException("Passkey does not exist.")
            }
        }
    }

    private fun mapResponse(result: FinishLoginResult.Logged): FinishLoginResponse {
        return FinishLoginResponse(
            tokens = accessRefreshTokenMapper.map(result.tokens),
            user = result.user
        )
    }

    private class RequestTimeoutException(message: String) : BaseHttpException(
        message,
        type = TYPE,
        status = HttpStatus.REQUEST_TIMEOUT,
        messageDetails = emptyMap(),
    ) {
        companion object {
            private val TYPE = URI.create("https://developer.mozilla.org/en-US/docs/Web/HTTP/Reference/Status/408")
        }
    }
}
