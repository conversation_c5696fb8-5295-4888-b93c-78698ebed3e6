package cz.deuss.userservice.passkey.registration

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.userservice.api.PasskeyRegistrationApi
import cz.deuss.userservice.api.model.FinishAdditionalPasskeyRegistrationRequest
import cz.deuss.userservice.api.model.FinishAdditionalPasskeyRegistrationResponse
import cz.deuss.userservice.api.model.StartAdditionalPasskeyRegistrationResponse
import cz.deuss.userservice.passkey.OriginProvider
import io.micronaut.http.HttpResponse
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule

@ExposedController
open class RegisterAdditionalPasskeyController(
    private val adapter: RegisterAdditionalPasskeyRestAdapter,
    private val originProvider: OriginProvider
) : PasskeyRegistrationApi {

    @Secured(SecurityRule.IS_AUTHENTICATED)
    override fun startAdditionalPasskeyRegistration(): StartAdditionalPasskeyRegistrationResponse {
        val origin = originProvider.requestOrigin();

        return adapter.startAdditionalPasskeyRegistration(origin)
    }

    @Secured(SecurityRule.IS_AUTHENTICATED)
    override fun finishAdditionalPasskeyRegistration(
        finishAdditionalPasskeyRegistrationRequest: FinishAdditionalPasskeyRegistrationRequest
    ): HttpResponse<FinishAdditionalPasskeyRegistrationResponse> {
        val origin = originProvider.requestOrigin();

        return adapter.finishAdditionalPasskeyRegistration(finishAdditionalPasskeyRegistrationRequest, origin)
    }
}
