package cz.deuss.userservice.passkey

import com.yubico.webauthn.AssertionRequest
import com.yubico.webauthn.AssertionResult
import com.yubico.webauthn.FinishAssertionOptions
import com.yubico.webauthn.StartAssertionOptions
import com.yubico.webauthn.data.AuthenticatorAssertionResponse
import com.yubico.webauthn.data.ByteArray
import com.yubico.webauthn.data.ClientAssertionExtensionOutputs
import com.yubico.webauthn.data.PublicKeyCredential
import com.yubico.webauthn.data.PublicKeyCredentialRequestOptions
import com.yubico.webauthn.data.UserVerificationRequirement
import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.userservice.api.model.LoginFinishRequest
import cz.deuss.userservice.api.model.LoginOptions
import cz.deuss.userservice.api.model.UserData
import cz.deuss.userservice.configuration.PasskeyProperties
import cz.deuss.userservice.configuration.WebAuthnConfig
import cz.deuss.userservice.database.model.DeussPasskey
import cz.deuss.userservice.database.model.DeussPasskeyChallenge
import cz.deuss.userservice.database.repository.PasskeyChallengeRepository
import cz.deuss.userservice.database.repository.PasskeyRepository
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.mapping.toUserData
import cz.deuss.userservice.service.TokenService
import cz.deuss.userservice.user.role.AssignedUserRolesService
import cz.deuss.userservice.validation.Invalid
import cz.deuss.userservice.validation.Valid
import cz.deuss.userservice.validation.ValidationResult
import io.micronaut.security.token.render.AccessRefreshToken
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.time.LocalDateTime
import java.util.UUID

typealias PasskeyChallengeValidationResult = ValidationResult<PasskeyChallengeValidationError, DeussPasskeyChallenge>

/**
 * Service responsible for passkey-based authentication and registration. Implements WebAuthn
 * protocol for passwordless authentication.
 */
@Singleton
open class PasskeyService(
    private val passkeyRepository: PasskeyRepository,
    private val challengeRepository: PasskeyChallengeRepository,
    private val tokenService: TokenService,
    private val passkeyProperties: PasskeyProperties,
    private val relyingParties: WebAuthnConfig.RelyingParties,
    private val assignedUserRolesService: AssignedUserRolesService,
) {

    /**
     * Initiates the login process with passkey authentication.
     *
     * @return Login options to be used by the client for authentication
     */
    @Transactional
    open fun startLogin(origin: String): LoginOptions {
        val rp = relyingParties.parties[origin] ?: throw BadRequestException("No rp handles origin $origin")

        val assertionRequest = rp.startAssertion(
            StartAssertionOptions.builder()
                .userVerification(UserVerificationRequirement.REQUIRED)
                .build()
        )

        val challenge = assertionRequest.publicKeyCredentialRequestOptions.challenge.base64
        val passkeyChallenge = createAndSaveChallenge(challenge = challenge)

        return LoginOptions(
            session = passkeyChallenge.id.toString(),
            challenge = challenge,
            allowCredentials = emptyList(),
            rpId = rp.identity.id,
            timeout = passkeyProperties.challengeTimeout.toMillis().toInt(),
        )
    }

    /**
     * Completes the login process after user has authenticated with their passkey.
     *
     * @param request Login finish request containing assertion data
     * @return Authentication success response with token and user info
     */
    @Transactional
    open fun finishLogin(request: LoginFinishRequest, origin: String): FinishLoginResult {
        val sessionId = UUID.fromString(request.session)

        val challenge = when (val result = findAndValidateChallenge(sessionId)) {
            is Invalid -> return FinishLoginResult.Challenge(result.error)
            is Valid -> result.data
        }

        val credential = buildAssertionCredential(request)
        val userId = credential.response.userHandle
            .map { userHandleBytes ->
                val userId = UUID.fromString(String(userHandleBytes.bytes))
                UserId(userId)
            }
            .orElse(null)
            ?: return FinishLoginResult.UserNotRecognized

        val passkey = passkeyRepository.getByCredentialIdAndActiveTrueAndUserId(
            credential.id.base64,
            userId.id
        ) ?: return FinishLoginResult.PasskeyNotFound

        val byteChallenge = challenge.challenge.passkeyBase64ToByteArray()
        val result = assertionResult(byteChallenge, passkey, credential, origin)

        // Update passkey with new signature counter
        updatePasskeyAfterLogin(passkey, result.signatureCount)

        // Mark challenge as used
        markChallengeAsUsed(challenge)

        // Generate both access and refresh tokens
        val assignedRoles = assignedUserRolesService.assignedRoles(userId).toSet()
        val tokens = tokenService.generateTokens(
            userId = UserId(passkey.user.id),
            email = passkey.user.email,
            roles = assignedRoles,
        )

        return FinishLoginResult.Logged(
            tokens = tokens,
            user = passkey.user.toUserData(assignedRoles),
        )
    }

    fun validateChallenge(challenge: DeussPasskeyChallenge?): PasskeyChallengeValidationResult {
        challenge ?: return Invalid(PasskeyChallengeValidationError.NotFoundOrUsed)

        if (challenge.hasExpired()) {
            return Invalid(PasskeyChallengeValidationError.Expired)
        }

        return Valid(challenge)
    }

    /**
     * Retrieve all passkey credentials registered for user which are active.
     */
    @Transactional
    open fun findUserPasskeyCredentials(userId: UserId): List<PasskeyCredential> {
        return passkeyRepository
            .getAllByUserIdAndActiveTrue(userId.id)
            .map { credential -> credential.toDomain() }
    }

    private fun assertionResult(
        byteChallenge: ByteArray,
        passkey: DeussPasskey,
        credential: PublicKeyCredential<AuthenticatorAssertionResponse, ClientAssertionExtensionOutputs>?,
        origin: String
    ): AssertionResult {
        val rp = relyingParties.parties[origin] ?: throw BadRequestException("No rp handles origin $origin")
        val result = rp.finishAssertion(
            FinishAssertionOptions.builder()
                .request(
                    AssertionRequest.builder()
                        .publicKeyCredentialRequestOptions(
                            PublicKeyCredentialRequestOptions.builder()
                                .challenge(byteChallenge)
                                .build()
                        )
                        .username(passkey.user.email)
                        .build()
                )
                .response(credential)
                .build()
        )

        check(result.isSuccess) { "Authentication failed" }
        return result
    }

    private fun createAndSaveChallenge(
        challenge: String,
    ): DeussPasskeyChallenge {
        return challengeRepository.save(
            DeussPasskeyChallenge(challenge, null).apply {
                this.expiresAt = LocalDateTime.now().plus(passkeyProperties.challengeTimeout)
                this.used = false
            }
        )
    }

    private fun findAndValidateChallenge(
        sessionId: UUID,
    ): PasskeyChallengeValidationResult {
        val challenge = challengeRepository.getByIdAndUserEmailIsEmptyAndUsedFalse(sessionId)

        return validateChallenge(challenge)
    }

    private fun buildAssertionCredential(request: LoginFinishRequest) =
        PublicKeyCredential
            .builder<AuthenticatorAssertionResponse, ClientAssertionExtensionOutputs>()
            .id(request.assertionResponse.id.passkeyBase64ToByteArray())
            .response(
                AuthenticatorAssertionResponse.builder()
                    .authenticatorData(
                        ByteArray(
                            request.assertionResponse
                                .response
                                .authenticatorData
                        )
                    )
                    .clientDataJSON(
                        ByteArray(
                            request.assertionResponse
                                .response
                                .clientDataJSON
                        )
                    )
                    .signature(
                        ByteArray(request.assertionResponse.response.signature)
                    )
                    .userHandle(request.assertionResponse.response.userHandle?.let { ByteArray(it) })
                    .build()
            )
            .clientExtensionResults(ClientAssertionExtensionOutputs.builder().build())
            .build()

    private fun markChallengeAsUsed(challenge: DeussPasskeyChallenge) {
        challenge.apply { used = true }.also { challengeRepository.update(it) }
    }

    private fun updatePasskeyAfterLogin(passkey: DeussPasskey, signatureCount: Long) {
        passkey
            .apply {
                updateLastUpdatedAt()
                signatureCounter = signatureCount
            }
            .also { passkeyRepository.update(it) }
    }

    companion object {
        /** Extension function to convert base64 string to ByteArray */
        internal fun String.passkeyBase64ToByteArray(): ByteArray {
            return ByteArray.fromBase64(this)
        }
    }
}

sealed interface FinishLoginResult {
    data class Logged(
        val tokens: AccessRefreshToken,
        val user: UserData,
    ) : FinishLoginResult

    data object PasskeyNotFound : FinishLoginResult
    data object UserNotRecognized : FinishLoginResult
    data class Challenge(val error: PasskeyChallengeValidationError) : FinishLoginResult
}


sealed interface PasskeyChallengeValidationError {
    data object NotFoundOrUsed : PasskeyChallengeValidationError

    // does exist, but it already expired
    data object Expired : PasskeyChallengeValidationError
}

