package cz.deuss.userservice.passkey

import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import io.micronaut.http.HttpRequest
import io.micronaut.http.context.ServerRequestContext
import jakarta.inject.Singleton

interface OriginProvider {
    /**
     * Provides the Origin header value from the current HTTP request or throws if missing.
     * @throws BadRequestException
     */
    fun requestOrigin(): String
}

@Singleton
class HttpRequestOriginProvider : OriginProvider {
    override fun requestOrigin(): String = ServerRequestContext.currentRequest<HttpRequest<*>>()
        .map { it.headers["Origin"] }.orElseThrow { BadRequestException("Origin header not set") }
}