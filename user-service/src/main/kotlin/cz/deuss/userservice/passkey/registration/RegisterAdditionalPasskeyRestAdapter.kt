package cz.deuss.userservice.passkey.registration

import cz.deuss.platform.offchain.framework.authentication.AuthenticationService
import cz.deuss.userservice.api.model.FinishAdditionalPasskeyRegistrationRequest
import cz.deuss.userservice.api.model.FinishAdditionalPasskeyRegistrationResponse
import cz.deuss.userservice.api.model.PasskeyCredential
import cz.deuss.userservice.api.model.StartAdditionalPasskeyRegistrationResponse
import cz.deuss.userservice.domain.user.toUserId
import io.micronaut.http.HttpResponse
import jakarta.inject.Singleton

/**
 * Adapter between REST controller and service layer.
 */
@Singleton
class RegisterAdditionalPasskeyRestAdapter(
    private val registerAdditionalPasskeyService: RegisterAdditionalPasskeyService,
    private val authenticatedUserData: () -> AuthenticationService.AuthenticationData,
) {

    fun startAdditionalPasskeyRegistration(origin: String): StartAdditionalPasskeyRegistrationResponse {
        val auth = authenticatedUserData()
        val options = registerAdditionalPasskeyService.startAdditionalPasskeyRegistration(
            userId = auth.userId.toUserId(),
            email = auth.email,
            origin = origin
        )

        return StartAdditionalPasskeyRegistrationResponse(options)
    }

    fun finishAdditionalPasskeyRegistration(
        request: FinishAdditionalPasskeyRegistrationRequest,
        origin: String
    ): HttpResponse<FinishAdditionalPasskeyRegistrationResponse> {

        val passkeyCredential = registerAdditionalPasskeyService.finishAdditionalPasskeyRegistration(
            userId = authenticatedUserData().userId.toUserId(),
            sessionId = request.passkey.session,
            attestationResponse = request.passkey.attestationResponse,
            friendlyName = request.passkey.friendlyName,
            origin = origin
        )

        val response = FinishAdditionalPasskeyRegistrationResponse(
            passkey = PasskeyCredential(
                id = passkeyCredential.id.id,
                friendlyName = passkeyCredential.friendlyName,
                lastUsedAt = passkeyCredential.lastUsedAt,
                lastUpdatedAt = passkeyCredential.lastUpdatedAt,
            )
        )

        return HttpResponse.created(response)
    }
}
