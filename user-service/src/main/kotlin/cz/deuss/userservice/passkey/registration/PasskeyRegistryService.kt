package cz.deuss.userservice.passkey.registration

import com.yubico.webauthn.FinishRegistrationOptions
import com.yubico.webauthn.RegistrationResult
import com.yubico.webauthn.RelyingParty
import com.yubico.webauthn.StartRegistrationOptions
import com.yubico.webauthn.data.AttestationObject
import com.yubico.webauthn.data.AuthenticatorAttachment
import com.yubico.webauthn.data.AuthenticatorAttestationResponse
import com.yubico.webauthn.data.AuthenticatorSelectionCriteria
import com.yubico.webauthn.data.AuthenticatorTransport
import com.yubico.webauthn.data.ByteArray
import com.yubico.webauthn.data.COSEAlgorithmIdentifier
import com.yubico.webauthn.data.ClientRegistrationExtensionOutputs
import com.yubico.webauthn.data.PublicKeyCredential
import com.yubico.webauthn.data.PublicKeyCredentialCreationOptions
import com.yubico.webauthn.data.PublicKeyCredentialParameters
import com.yubico.webauthn.data.PublicKeyCredentialType
import com.yubico.webauthn.data.RelyingPartyIdentity
import com.yubico.webauthn.data.ResidentKeyRequirement
import com.yubico.webauthn.data.UserIdentity
import com.yubico.webauthn.data.UserVerificationRequirement
import cz.deuss.platform.offchain.chain.DeussChain
import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.userservice.api.model.AttestationResponse
import cz.deuss.userservice.api.model.RegistrationOptions
import cz.deuss.userservice.api.model.RegistrationOptionsAuthenticatorSelection
import cz.deuss.userservice.api.model.RegistrationOptionsPubKeyCredParamsInner
import cz.deuss.userservice.api.model.RegistrationOptionsRp
import cz.deuss.userservice.api.model.RegistrationOptionsUser
import cz.deuss.userservice.configuration.PasskeyProperties
import cz.deuss.userservice.configuration.WebAuthnConfig
import cz.deuss.userservice.database.model.DeussPasskey
import cz.deuss.userservice.database.model.DeussPasskeyChallenge
import cz.deuss.userservice.database.model.DeussUser
import cz.deuss.userservice.database.model.DeussUserOnchainAccount
import cz.deuss.userservice.database.repository.PasskeyChallengeRepository
import cz.deuss.userservice.database.repository.PasskeyRepository
import cz.deuss.userservice.database.repository.UserOnchainAccountRepository
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.passkey.PasskeyCredential
import cz.deuss.userservice.passkey.PasskeyService
import cz.deuss.userservice.passkey.PasskeyService.Companion.passkeyBase64ToByteArray
import cz.deuss.userservice.passkey.toDomain
import cz.deuss.userservice.service.UserDataInPasskeyChallenge
import jakarta.inject.Singleton
import java.math.BigInteger
import java.security.interfaces.ECPublicKey
import java.time.LocalDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

/**
 * Register user passkeys -> user can have multiple passkeys.
 * Does not contain any other logic related to actual registration flows.
 */
@Singleton
open class PasskeyRegistryService(
    private val passkeyRepository: PasskeyRepository,
    private val userOnchainAccountRepository: UserOnchainAccountRepository,
    private val challengeRepository: PasskeyChallengeRepository,
    private val relyingParties: WebAuthnConfig.RelyingParties,
    private val passkeyProperties: PasskeyProperties,
    private val passkeyService: PasskeyService,
    private val deussChain: DeussChain,
) {

    private val logger = DeussLogger.semanticLogger(this::class)

    /**
     * Initiates the registration process for a new passkey.
     *
     * @return Registration options to be used by the client to create a credential
     */
    fun startRegistration(
        userId: UserId,
        email: String,
        origin: String
    ): RegistrationOptions {
        logger.action("passkeyStartRegistration")
            .started()
            .param("userId", userId.id)
            .param("email", email)
            .param("origin", origin)
            .info()

        val userIdentity = createUserIdentity(email, userId)

        val rp = relyingParties.parties[origin] ?: throw BadRequestException("No rp handles origin $origin")
        val registrationOptions = createRegistrationOptions(userIdentity, rp)

        val passkeyChallenge = createAndSaveChallenge(
            userEmail = email,
            userId = userId,
            challenge = registrationOptions.challenge.base64
        )

        return mapToRegistrationOptions(registrationOptions, passkeyChallenge).also {
            logger.action("passkeyStartRegistration")
                .finished()
                .param("rpId", rp.identity.id)
                .info()
        }
    }

    /**
     * Completes the registration process after user has created a credential.
     */
    fun finishRegistration(
        sessionId: UUID,
        user: DeussUser,
        attestation: AttestationResponse,
        friendlyName: String,
        origin: String
    ): PasskeyCredential {
        val rp = relyingParties.parties[origin] ?: throw BadRequestException("No rp handles origin $origin")
        val validChallenge = findAndValidateRegistrationChallenge(sessionId)

        val credential = buildRegistrationCredential(attestation)
        val credentialOptions = buildCredentialCreationOptions(validChallenge, user.email, rp)

        val result = rp.finishRegistration(
            FinishRegistrationOptions.builder()
                .request(credentialOptions)
                .response(credential)
                .build()
        )

        markChallengeAsUsed(validChallenge)

        val passkey = savePasskey(user, credential, result, friendlyName)

        val savePasskeyResult = deussChain.saveUserPasskey(BigInteger(result.keyId.id.hex, 16), ecPublicKey(result))
        when (savePasskeyResult) {
            is DeussChain.ChainResult.Ok<DeussChain.PasskeySmartAccount> -> {
                saveUserPasskeyAccount(savePasskeyResult.value, passkey)
            }

            is DeussChain.ChainResult.Err -> {
                // In the saveUserPasskey call, there is no error onchain, so only errors can occur in transmission
                // of the call, or (de)serialization, which should be covered by tests.
                //
                // So in a case of an unexpected error, I choose to rather log it and let the registration finish,
                // because user already has the passkey saved. If I returned an error, that won't delete users passkey.
                // We can also retroactively publish the passkey onchain later, since we have it saved.
                logger.error { "couldn't save users passkey on chain ${savePasskeyResult.error}" }
            }
        }

        return passkey.toDomain()
    }

    fun validateAndExtractRegistrationChallenge(
        sessionId: UUID,
    ): UserDataInPasskeyChallenge {
        val challengeEntity = findAndValidateRegistrationChallenge(sessionId)
        val email = challengeEntity.userEmail
            ?: throw IllegalArgumentException("User email not found in challenge")

        val userId = challengeEntity.userId
            ?: throw IllegalArgumentException("User ID not found in challenge")

        return UserDataInPasskeyChallenge(
            userId = UserId(userId),
            email = email,
        )
    }

    private fun findAndValidateRegistrationChallenge(
        sessionId: UUID,
    ): DeussPasskeyChallenge {
        val challenge = challengeRepository.getByIdAndUserEmailIsNotEmptyAndUsedFalse(sessionId)

        return passkeyService
            .validateChallenge(challenge)
            .validOrThrow { error -> error(error.toString()) }
    }

    private fun buildRegistrationCredential(
        attestation: AttestationResponse
    ): PublicKeyCredential<AuthenticatorAttestationResponse, ClientRegistrationExtensionOutputs> {
        val attestationObject =
            AttestationObject(ByteArray(attestation.attestationObject))

        // `transports` is a variable that stores a mutable set of `AuthenticatorTransport` objects.
        val transports =
            // Accesses the `transports` field from `request.attestationResponse`.
            attestation.transports
                ?.mapNotNull {
                    // Tries to convert each transport string to an `AuthenticatorTransport` object.
                    // If successful, returns it; if there's an exception, returns null.
                    runCatching { AuthenticatorTransport.of(it) }.getOrNull()
                }
                // Converts the list of non-null `AuthenticatorTransport` objects to a mutable set.
                ?.toMutableSet()


        val authenticatorResponse =
            AuthenticatorAttestationResponse.builder()
                .attestationObject(attestationObject.bytes)
                .clientDataJSON(ByteArray(attestation.clientDataJSON))
                .transports(transports)
                .build()

        val attestedCredentialData = attestationObject
            .authenticatorData
            .attestedCredentialData
            .orElseThrow { IllegalStateException("Attestation data not found") }

        return PublicKeyCredential
            .builder<AuthenticatorAttestationResponse, ClientRegistrationExtensionOutputs>()
            .id(attestedCredentialData.credentialId)
            .response(authenticatorResponse)
            .clientExtensionResults(ClientRegistrationExtensionOutputs.builder().build())
            .build()
    }

    private fun buildCredentialCreationOptions(
        challenge: DeussPasskeyChallenge,
        email: String,
        rp: RelyingParty,
    ): PublicKeyCredentialCreationOptions {
        return PublicKeyCredentialCreationOptions.builder()
            .rp(
                RelyingPartyIdentity.builder()
                    .id(rp.identity.id)
                    .name(rp.identity.name)
                    .build()
            )
            .user(
                UserIdentity.builder()
                    .name(email)
                    .displayName(email)
                    .id(ByteArray(challenge.id.toString().toByteArray()))
                    .build()
            )
            .challenge(challenge.challenge.passkeyBase64ToByteArray())
            .pubKeyCredParams(
                listOf(
                    // TODO VS this is incorrect I believe. options returned when register started allow more. this should mirror those options.
                    PublicKeyCredentialParameters.builder()
                        .alg(COSEAlgorithmIdentifier.ES256)
                        .type(PublicKeyCredentialType.PUBLIC_KEY)
                        .build()
                )
            )
            .build()
    }

    private fun savePasskey(
        user: DeussUser,
        credential:
        PublicKeyCredential<AuthenticatorAttestationResponse, ClientRegistrationExtensionOutputs>,
        result: RegistrationResult,
        friendlyName: String,
    ): DeussPasskey {
        @Suppress("DEPRECATION")
        // isBackupEligible and isBackedUp are experimental and their API is subject to change
        // More can be found in the yubico/java-webauthn-server library docs
        return passkeyRepository.save(
            DeussPasskey(
                user = user,
                credentialId = result.keyId.id.base64,
                publicKeyCose = result.publicKeyCose.bytes,
                clientData = credential.response.clientDataJSON.bytes,
                attestedCredentialData =
                    credential.response.attestationObject.bytes,
                signatureCounter = result.signatureCount,
                backupEligible = result.isBackupEligible,
                backupState = result.isBackedUp,
                userFriendlyName = friendlyName,
            )

        )
    }

    private fun saveUserPasskeyAccount(
        smartAccount: DeussChain.PasskeySmartAccount,
        passkey: DeussPasskey,
    ): DeussUserOnchainAccount {
        return userOnchainAccountRepository.save(
            DeussUserOnchainAccount(
                id = UUID.randomUUID(),
                address = smartAccount.account,
                passkey = passkey,
                name = null
            )
        )
    }

    private fun markChallengeAsUsed(challenge: DeussPasskeyChallenge) {
        challenge.apply { used = true }.also { challengeRepository.update(it) }
    }

    private fun createUserIdentity(email: String, userId: UserId): UserIdentity {
        return UserIdentity.builder()
            .name(email)
            .displayName(email)
            .id(ByteArray(userId.id.toString().toByteArray()))
            .build()
    }

    protected fun createRegistrationOptions(
        userIdentity: UserIdentity,
        relyingParty: RelyingParty,
    ): PublicKeyCredentialCreationOptions {
        return relyingParty.startRegistration(
            StartRegistrationOptions.builder()
                .user(userIdentity)
                .authenticatorSelection(
                    AuthenticatorSelectionCriteria.builder()
                        .residentKey(ResidentKeyRequirement.REQUIRED)
                        .userVerification(UserVerificationRequirement.REQUIRED)
                        .authenticatorAttachment(AuthenticatorAttachment.PLATFORM)
                        .build()
                )
                .build()
        )
    }


    private fun mapToRegistrationOptions(
        registrationOptions: PublicKeyCredentialCreationOptions,
        passkeyChallenge: DeussPasskeyChallenge
    ): RegistrationOptions {
        return RegistrationOptions(
            session = passkeyChallenge.id,
            rp = RegistrationOptionsRp(
                id = registrationOptions.rp.id,
                name = registrationOptions.rp.name,
            ),
            user = RegistrationOptionsUser(
                id = registrationOptions.user.id.base64,
                name = registrationOptions.user.name,
                displayName = registrationOptions.user.displayName,
            ),
            challenge = registrationOptions.challenge.base64,
            pubKeyCredParams = registrationOptions.pubKeyCredParams.map {
                RegistrationOptionsPubKeyCredParamsInner().apply {
                    this.type = it.type.id
                    this.alg = it.alg.id.toBigDecimal()
                }
            },
            timeout = passkeyProperties.challengeTimeout.toMillis().toInt(),
            authenticatorSelection = RegistrationOptionsAuthenticatorSelection(
                authenticatorAttachment =
                    registrationOptions
                        .authenticatorSelection
                        ?.getOrNull()
                        ?.authenticatorAttachment
                        ?.getOrNull()
                        ?.value
                        ?: throw IllegalStateException("authenticatorAttachment is null"),
                residentKey =
                    registrationOptions
                        .authenticatorSelection
                        ?.getOrNull()
                        ?.residentKey
                        ?.getOrNull()
                        ?.value
                        ?: throw IllegalStateException("residentKey is null"),
                userVerification =
                    registrationOptions
                        .authenticatorSelection
                        ?.getOrNull()
                        ?.userVerification
                        ?.getOrNull()
                        ?.value
                        ?: throw IllegalStateException("userVerification is null")
            ),
            attestation = registrationOptions.attestation.value,
            excludeCredentials = emptyList()
        )
    }

    private fun createAndSaveChallenge(
        userEmail: String,
        userId: UserId,
        challenge: String
    ): DeussPasskeyChallenge {
        return challengeRepository.save(
            DeussPasskeyChallenge(challenge, userEmail).apply {
                this.userId = userId.id
                this.expiresAt = LocalDateTime.now().plus(passkeyProperties.challengeTimeout)
                this.used = false
            }
        )
    }

    private fun ecPublicKey(result: RegistrationResult): DeussChain.PublicKey {
        val key = result.parsedPublicKey
        if (key !is ECPublicKey) {
            throw IllegalArgumentException("only EC keys are supported")
        }
        return DeussChain.PublicKey.fromBigInts(key.w.affineX, key.w.affineY)
    }
}
