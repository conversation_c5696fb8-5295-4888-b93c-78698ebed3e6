package cz.deuss.userservice.passkey.registration

import cz.deuss.platform.offchain.framework.exceptions.BadRequestException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.api.model.AttestationResponse
import cz.deuss.userservice.api.model.RegistrationOptions
import cz.deuss.userservice.configuration.PasskeyProperties
import cz.deuss.userservice.database.repository.UserRepository
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.passkey.PasskeyCredential
import io.micronaut.context.annotation.Value
import io.micronaut.http.HttpRequest
import io.micronaut.http.context.ServerRequestContext
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.util.UUID

/**
 * Handle flow: User want to register additional passkey credential.
 */
@Singleton
open class RegisterAdditionalPasskeyService(
    private val passkeyRegistryService: PasskeyRegistryService,
    private val userRepository: UserRepository,
    private val passkeyProperties: PasskeyProperties,
) {

    @Transactional
    open fun startAdditionalPasskeyRegistration(
        userId: UserId,
        email: String,
        origin: String
    ): RegistrationOptions {
        return passkeyRegistryService.startRegistration(userId, email, origin)
    }

    @Transactional
    open fun finishAdditionalPasskeyRegistration(
        userId: UserId,
        sessionId: UUID,
        attestationResponse: AttestationResponse,
        friendlyName: String?,
        origin: String
    ): PasskeyCredential {
        return passkeyRegistryService.finishRegistration(
            sessionId = sessionId,
            user = userRepository.getById(userId.id) ?: throw NotFoundException("User=${userId.id} not found"),
            attestation = attestationResponse,
            friendlyName = friendlyName ?: passkeyProperties.additional.defaultFriendlyName,
            origin
        )
    }
}
