package cz.deuss.userservice.registration

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.exceptions.ConflictException
import cz.deuss.platform.offchain.framework.exceptions.InternalServerErrorException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.userservice.api.UserPasskeyRegistrationApi
import cz.deuss.userservice.api.model.FinishPasskeyRegistrationInput
import cz.deuss.userservice.api.model.FinishUserRegistrationRequest
import cz.deuss.userservice.api.model.RegistrationOptions
import cz.deuss.userservice.api.model.RegistrationStartRequest
import cz.deuss.userservice.api.model.RegistrationSuccessResponse
import cz.deuss.userservice.passkey.OriginProvider
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.http.HttpResponse
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule

@ExposedController
@ExecuteOn(TaskExecutors.BLOCKING)
@Secured(SecurityRule.IS_ANONYMOUS)
open class UserPasskeyRegistrationController(
    private val registrationService: UserRegistrationService,
    private val originProvider: OriginProvider
) : UserPasskeyRegistrationApi {

    private val logger = DeussLogger.semanticLogger(this::class)

    override fun startUserRegistration(registrationStartRequest: RegistrationStartRequest): RegistrationOptions {
        val origin = originProvider.requestOrigin();
        return try {
            registrationService.startRegistration(
                registrationStartRequest.email,
                registrationStartRequest.inviteToken,
                origin
            )
        } catch (e: ConflictException) {
            // Used email etc. Only non obscured exception
            throw e
        } catch (e: NotFoundException) {
            // for invalid invitation token
            throw e
        } catch (e: Exception) {
            logger.error(e) { "An error occurred while starting registration" }
            throw InternalServerErrorException("An error occurred while starting registration")
        }
    }

    override fun finishUserRegistration(
        finishUserRegistrationRequest: FinishUserRegistrationRequest
    ): HttpResponse<RegistrationSuccessResponse> {
        val origin = originProvider.requestOrigin();
        return try {
            val req = finishUserRegistrationRequest
            // TODO: backward compatibility until FE migrates. then remove req.session and res.attestationResponse & make passkey required.
            val passkey = FinishPasskeyRegistrationInput(
                session = req.passkey?.session ?: req.session!!,
                attestationResponse = req.passkey?.attestationResponse ?: req.attestationResponse!!,
                friendlyName = req.passkey?.friendlyName
            )
            val response = registrationService.finishRegistration(
                req.userData,
                passkey,
                origin
            )

            HttpResponse.created(response)
        } catch (e: Exception) {
            logger.error(e) { "An error occurred while finishing registration" }
            throw InternalServerErrorException("An error occurred while finishing registration")
        }
    }
}
