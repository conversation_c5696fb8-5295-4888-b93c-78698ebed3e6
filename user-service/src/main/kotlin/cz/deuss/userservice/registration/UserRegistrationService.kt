package cz.deuss.userservice.registration

import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.ConflictException
import cz.deuss.userservice.api.model.FinishPasskeyRegistrationInput
import cz.deuss.userservice.api.model.RegistrationOptions
import cz.deuss.userservice.api.model.RegistrationSuccessResponse
import cz.deuss.userservice.api.model.UserRegistrationData
import cz.deuss.userservice.configuration.PasskeyProperties
import cz.deuss.userservice.database.model.DeussUser
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.passkey.registration.PasskeyRegistryService
import cz.deuss.userservice.service.InviteTokenPersistenceService
import cz.deuss.userservice.service.UserService
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.util.*

@Singleton
open class UserRegistrationService(
    private val passkeyService: PasskeyRegistryService,
    private val userService: UserService,
    private val inviteTokenPersistenceService: InviteTokenPersistenceService,
    private val passkeyProperties: PasskeyProperties,
) {

    /**
     * Initiates the registration process for a new user with passkey.
     *
     * @return Registration options to be used by the client to create a credential
     */
    @Transactional
    open fun startRegistration(
        email: String,
        inviteToken: UUID,
        origin: String
    ): RegistrationOptions {
        val validEmail = validateEmail(email)
        inviteTokenPersistenceService.findAndValidateToken(inviteToken)

        // Check if user already exists
        val existingUser = userService.findByEmailOrNull(validEmail)
        if (existingUser != null) {
            throw ConflictException("User with email $validEmail already exists")
        }
        // TODO: fix according to
        // https://gitlab.nesad.fit.vutbr.cz/bebi/offchain-microservices-core/offchain/-/merge_requests/13#note_12040
        val userId = UserId(UUID.randomUUID())

        inviteTokenPersistenceService.useToken(inviteToken, userId.id)

        return passkeyService.startRegistration(userId, validEmail, origin)
    }

    /**
     * Completes the registration process after user has created a credential.
     *
     * @param passkeyInput Registration finish request containing attestation
     * @return Registration success response
     */
    @Transactional
    open fun finishRegistration(
        userDataInput: UserRegistrationData,
        passkeyInput: FinishPasskeyRegistrationInput,
        origin: String
    ): RegistrationSuccessResponse {
        val sessionId = passkeyInput.session
        val userData = passkeyService.validateAndExtractRegistrationChallenge(sessionId)

        val user = userService.create(
            // TODO some domain object instead of entity would be better.
            DeussUser(
                email = userData.email,
                id = userData.userId.id,
                firstName = userDataInput.firstName,
                lastName = userDataInput.lastName,
            ),
            roles = setOf(Role.Enum.USER),
        )

        passkeyService.finishRegistration(
            sessionId = sessionId,
            user = user,
            attestation = passkeyInput.attestationResponse,
            friendlyName = passkeyInput.friendlyName ?: passkeyProperties.additional.defaultFriendlyName,
            origin = origin
        )

        return RegistrationSuccessResponse(id = user.id, email = user.email)
    }


    private fun validateEmail(email: String): String {
        val userEmail = email.trim()
        require(userEmail.isNotEmpty()) { "Email cannot be empty" }

        return userEmail
    }
}
