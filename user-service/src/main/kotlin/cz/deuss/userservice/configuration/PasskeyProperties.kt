package cz.deuss.userservice.configuration

import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import io.micronaut.core.bind.annotation.Bindable
import io.micronaut.serde.annotation.Serdeable
import java.time.Duration

@ConfigurationProperties("passkey")
data class PasskeyProperties(
    val challengeTimeout: Duration,
    val registration: RegistrationProperties,
    val additional: AdditionalProperties,
)

@Serdeable
@EachProperty("passkey.relying-parties")
data class RelyingPartyProperties(
    @param:Parameter val name: String,
    val rpId: String,
    val rpName: String,
    val origin: String
)

@ConfigurationProperties("passkey.registration")
data class RegistrationProperties(
    val defaultFriendlyName: String
)

@ConfigurationProperties("passkey.additional")
data class AdditionalProperties(
    val defaultFriendlyName: String
)