package cz.deuss.userservice.configuration

import com.yubico.webauthn.RelyingParty
import com.yubico.webauthn.data.AttestationConveyancePreference
import com.yubico.webauthn.data.RelyingPartyIdentity
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import cz.deuss.userservice.adapter.CredentialRepositoryAdapter
import cz.deuss.userservice.database.repository.PasskeyRepository
import cz.deuss.userservice.database.repository.UserRepository
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory

@Factory
class WebAuthnConfig {

    private val logger = DeussLogger.semanticLogger(this::class)

    data class RelyingParties(val parties: Map<String, RelyingParty>)

    @Bean
    fun relyingParties(
        relyingParties: List<RelyingPartyProperties>,
        passkeyRepository: PasskeyRepository,
        userRepository: UserRepository
    ): RelyingParties {
        val log = logger.message("Creating passkey relaying parties")

        val partiesByOrigin: Map<String, RelyingParty> = relyingParties.associateBy(
            keySelector = { it.origin },
            valueTransform = { party ->
                log.param("relayingParty-${party.name}", party.toString())
                val rpIdentity = RelyingPartyIdentity.builder()
                    .id(party.rpId)
                    .name(party.rpName)
                    .build()

                RelyingParty.builder()
                    .identity(rpIdentity)
                    .credentialRepository(CredentialRepositoryAdapter(passkeyRepository, userRepository))
                    .origins(setOf(party.origin))
                    .attestationConveyancePreference(AttestationConveyancePreference.NONE)
                    .build()
            }
        )

        log.info();
        return RelyingParties(partiesByOrigin)
    }
}