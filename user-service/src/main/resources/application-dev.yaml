micronaut:
  server:
    port: 8080
  security:
    intercept-url-map:
      - pattern: /api/exposed/openapi/**
        access: isAnonymous()
      - pattern: /api/exposed/**
        access: isAuthenticated()
  router:
    static-resources:
      openapi-yaml:
        paths: classpath:openapi
        mapping: /api/exposed/openapi/**
passkey:
  relying-parties:
    dev:
      rp-id: ${PASSKEY_ORIGIN:`https://dev.deussblockchain.eu`}
      rp-name: ${PASSKEY_RP_NAME:`Deuss EU`}
      origin: ${PASSKEY_ORIGIN:`https://dev.deussblockchain.eu`} # Address of DEV frontend
    # Localhost FE, so that Passkeys can be registered from local FE on DEV offchain
    local:
      rp-id: "localhost"
      rp-name: "Deuss EU"
      origin: "http://localhost:4200"
