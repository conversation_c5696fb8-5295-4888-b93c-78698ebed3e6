# If you need to debug why are your requests failing on 401 Unauthorized
#logger:
#  levels:
#    io.micronaut.security: DEBUG

micronaut:
  security:
    token:
      jwt:
        signatures:
          secret:
            generator:
              secret: ${JWT_GENERATOR_SIGNATURE_SECRET:pleaseChangeThisSecretForANewOne}
              jws-algorithm: HS256
        generator:
          refresh-token:
            secret: ${JWT_GENERATOR_SIGNATURE_SECRET:pleaseChangeThisSecretForANewOne}
            jws-algorithm: HS256

datasources:
  default:
    db-type: postgres
    dialect: POSTGRES
    driver-class-name: org.postgresql.Driver
    url: ************************************************
    username: user_service_user
    password: user_service
jpa:
  default:
    properties:
      hibernate:
        show_sql: true
passkey:
  relying-parties:
    # By also including this DEV RP, we can simulate if a correct relaying party is picked
    # when testing with local FE
    dev:
      rp-id: ${PASSKEY_ORIGIN:`https://dev.deussblockchain.eu`}
      rp-name: ${PASSKEY_RP_NAME:`Deuss EU`}
      origin: ${PASSKEY_ORIGIN:`https://deussblockchain.eu`} # Address of DEV frontend
    # For local testing with html in `example` directory
    local-idea:
      rp-id: "localhost"
      rp-name: "Deuss EU"
      origin: "http://localhost:63342"
    # Localhost FE
    local-fe:
      rp-id: "localhost"
      rp-name: "Deuss EU"
      origin: "http://localhost:4200"
  challenge-timeout: PT5M
  registration:
    default-friendly-name: "Registration"
  additional:
    default-friendly-name: "Additional"

mailing:
  username: ${SMTP_USERNAME:placeholderToNotFailAtStart}
  password: ${SMTP_PASSWORD:placeholderToNotFailAtStart}
