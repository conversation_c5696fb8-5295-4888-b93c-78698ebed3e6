package cz.deuss.companyservice.controller.company

import cz.deuss.companyservice.BaseCompanyServiceTest
import cz.deuss.companyservice.api.model.*
import cz.deuss.companyservice.database.model.Employee
import cz.deuss.platform.offchain.framework.api.model.HttpError
import cz.deuss.platform.offchain.framework.api.model.MessageDetail
import cz.deuss.platform.offchain.framework.authentication.Role
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.test.annotation.Sql
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.net.URI
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import cz.deuss.companyservice.api.model.CountryCode as ApiCountryCode

@Sql(scripts = ["classpath:sql/company_upsert.sql"], phase = Sql.Phase.BEFORE_EACH)
class CreateCompanyTests : BaseCompanyServiceTest() {


    @Test
    fun `company can be created`() {
        val companyToCreate = createCompanyCreateRequest()

        val request = HttpRequest
            .POST(COMPANIES_PATH, companyToCreate)
            .bearerAuth(generateToken(roles = setOf(Role.Enum.ADMIN)))
        val response =
            exposedClient.exchange(request, CompanyDetailResponse::class.java)

        assertThat(response.status).isEqualTo(HttpStatus.CREATED)
        assertThat(response.body()).isNotNull
        val (createdCompany, mappedCompany) = databaseUtils.runInTransaction {
            val createdCompany = companyRepository.findAll().firstOrNull { it.iban == companyToCreate.iban }
            assertThat(createdCompany).isNotNull
            createdCompany to companyMapper.toDetail(createdCompany!!, Employee.Role.ADMIN)
        }

        assertThat(createdCompany!!)
            .usingRecursiveComparison()
            .comparingOnlyFields(
                CompanyCreateRequest::businessId.name,
                CompanyCreateRequest::vatNumber.name,
                CompanyCreateRequest::website.name,
                CompanyCreateRequest::registrationDate.name,
                CompanyCreateRequest::iban.name,
                CompanyCreateRequest::registrationEntity.name,
                CompanyCreateRequest::contactEmail.name,
                CompanyCreateRequest::contactPhone.name,
                CompanyCreateRequest::legalEntityId.name,
            ).isEqualTo(companyToCreate)
        assertThat(createdCompany.name).isEqualTo(companyToCreate.companyName)

        assertThat(response.body()).usingRecursiveComparison().withEqualsForType(
            { t1, t2 -> t1.truncatedTo(ChronoUnit.MILLIS) == t2.truncatedTo(ChronoUnit.MILLIS) },
            LocalDateTime::class.java
        ).isEqualTo(mappedCompany)
    }

    @Test
    fun `company can be created without website`() {
        val companyToCreate = createCompanyCreateRequest(website = null)

        val request = HttpRequest
            .POST(COMPANIES_PATH, companyToCreate)
            .bearerAuth(generateToken(roles = setOf(Role.Enum.ADMIN)))
        val response = exposedClient.exchange(request, CompanyDetailResponse::class.java)

        assertThat(response.status).isEqualTo(HttpStatus.CREATED)
        assertThat(response.body()).isNotNull
        assertThat(response.body().website).isNull()

        databaseUtils.runInTransaction {
            val createdCompany = companyRepository.findAll().firstOrNull { it.iban == companyToCreate.iban }
            assertThat(createdCompany).isNotNull
            assertThat(createdCompany!!.website).isNull()
        }
    }


    @ParameterizedTest
    @MethodSource("duplicateFiledNamesSupplier")
    fun `company with duplicate fields cannot be created and conflict 409 is returned`(
        vatNumber: String,
        website: String,
        iban: String,
        duplicateFieldNames: List<String>,
    ) {
        val countBefore = companyRepository.count()
        val companyToCreate = createCompanyCreateRequest(vatNumber = vatNumber, website = website, iban = iban)
        val expectedError = createConflictError(
            "Some attributes are already present",
            listOf(MessageDetail("duplicates", duplicateFieldNames)),
            URI.create(COMPANIES_PATH)
        )

        val response = assertThrows<HttpClientResponseException> {
            val request = HttpRequest
                .POST(COMPANIES_PATH, companyToCreate)
                .bearerAuth(generateToken())
            exposedClient.exchange(request, HttpError::class.java)
        }

        assertThat(response.status).isEqualTo(HttpStatus.CONFLICT)
        // TODO also check the error details - in current version it does not work
//        HttpErrorAssert.assertThat(response.extractError()).isEqualTo(expectedError)
        val countAfter = companyRepository.count()
        assertThat(countAfter).isEqualTo(countBefore)
    }

    @ParameterizedTest
    @MethodSource("invalidCreateCompaniesSupplier")
    fun `bad request 400 is returned, when data are malformed`(
        malformedData: CompanyCreateRequest,
        error: String,
    ) {
        val countBefore = companyRepository.count()
        val expectedError = createBadRequestError(
            detail = error,
            instance = URI.create(COMPANIES_PATH)
        )

        val response = assertThrows<HttpClientResponseException> {

            val request = HttpRequest
                .POST(COMPANIES_PATH, malformedData)
                .bearerAuth(generateToken())
            exposedClient.exchange(request, HttpError::class.java)
        }

        assertThat(response.status).isEqualTo(HttpStatus.BAD_REQUEST)
        // TODO also check the error details - in current version it does not work
//        HttpErrorAssert.assertThat(response.extractError()).isEqualTo(expectedError)
        val countAfter = companyRepository.count()
        assertThat(countAfter).isEqualTo(countBefore)
    }


    private companion object {
        private const val COMPANIES_PATH = "/companies"


        private fun createCompanyCreateRequest(
            companyName: String = "ŠtengCorp",
            businessId: String = "14254151543144512243",
            vatNumber: String = "CZ15421512",
            website: String? = "https://hoogle.haskell.org/",
            registrationDate: LocalDate = LocalDate.now(),
            iban: String = "CZ65 0800 0000 1920 0014 5399",
            registrationEntity: ApiCountryCode = ApiCountryCode.PL,
            contactEmail: String = "<EMAIL>",
            contactPhone: String = "+420606060606",
            legalEntityId: String = "5493001KJTIIGC8Y1R12",
        ): CompanyCreateRequest = CompanyCreateRequest(
            companyName = companyName,
            businessId = businessId,
            vatNumber = vatNumber,
            website = website,
            registrationDate = registrationDate,
            iban = iban,
            registrationEntity = registrationEntity,
            headcount = HeadcountCategory.MEDIUM,
            annualTurnoverEur = AnnualTurnoverCategory.MEDIUM,
            balanceSheetTotalEur = BalanceSheetTotalCategory.MEDIUM,
            ownershipStatus = OwnershipStatus.AUTONOMOUS,
            contactEmail = contactEmail,
            contactPhone = contactPhone,
            legalEntityId = legalEntityId
        )


        @JvmStatic
        private fun duplicateFiledNamesSupplier(): List<Arguments> = listOf(
            Arguments.of("CZ12345998", "unique", "unique", listOf(CompanyCreateRequest.JSON_PROPERTY_VAT_NUMBER)),
            Arguments.of(
                "CZ12345998", "unique", "**********************", listOf(
                    CompanyCreateRequest.JSON_PROPERTY_VAT_NUMBER,
                    CompanyCreateRequest.JSON_PROPERTY_IBAN
                )
            ),
            Arguments.of(
                "CZ12345998", "https://fjfi.cz/", "**********************", listOf(
                    CompanyCreateRequest.JSON_PROPERTY_VAT_NUMBER,
                    CompanyCreateRequest.JSON_PROPERTY_IBAN,
                    CompanyCreateRequest.JSON_PROPERTY_WEBSITE
                )
            ),
            Arguments.of("unique", "https://fjfi.cz/", "unique", listOf(CompanyCreateRequest.JSON_PROPERTY_WEBSITE)),
            Arguments.of("unique", "unique", "**********************", listOf(CompanyCreateRequest.JSON_PROPERTY_IBAN))
        )

        @JvmStatic
        private fun invalidCreateCompaniesSupplier(): List<Arguments> = listOf(
            Arguments.of(
                createCompanyCreateRequest("", "", "", "", LocalDate.now().plusYears(17), ""),
                "createNewCompany.companyCreateRequest.iban: must not be blank, createNewCompany.companyCreateRequest.vatNumber: must not be blank, createNewCompany.companyCreateRequest.businessId: must not be blank, createNewCompany.companyCreateRequest.website: must not be blank, createNewCompany.companyCreateRequest.companyName: must not be blank, createNewCompany.companyCreateRequest.registrationDate: registration_date must be in past or present"
            ),
            Arguments.of(
                createCompanyCreateRequest(
                    generateRandomString(),
                    generateRandomString(),
                    generateRandomString(),
                    generateRandomString(10_000),
                    LocalDate.now(),
                    generateRandomString()
                ),
                "createNewCompany.companyCreateRequest.iban: size must be between 0 and 255, createNewCompany.companyCreateRequest.businessId: size must be between 0 and 255, createNewCompany.companyCreateRequest.companyName: size must be between 0 and 255, createNewCompany.companyCreateRequest.website: size must be between 0 and 2048, createNewCompany.companyCreateRequest.vatNumber: size must be between 0 and 50"
            ),
            Arguments.of(
                createCompanyCreateRequest(
                    "     ",
                    "      ",
                    "            ",
                    "   ",
                    LocalDate.now(),
                    "       "
                ),
                "createNewCompany.companyCreateRequest.companyName: must not be blank, createNewCompany.companyCreateRequest.website: must not be blank, createNewCompany.companyCreateRequest.iban: must not be blank, createNewCompany.companyCreateRequest.businessId: must not be blank, createNewCompany.companyCreateRequest.vatNumber: must not be blank"
            ),
            Arguments.of(
                createCompanyCreateRequest(contactEmail = "michael.scott@"),
                "createNewCompany.companyCreateRequest.contactEmail: invalid format"
            ),
            Arguments.of(
                createCompanyCreateRequest(contactPhone = "420 *********"),
                "createNewCompany.companyCreateRequest.contactPhone: invalid format"
            ),
        )
    }
}
