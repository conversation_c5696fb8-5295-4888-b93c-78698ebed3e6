package cz.deuss.companyservice.controller

import cz.deuss.companyservice.api.CompaniesApi
import cz.deuss.companyservice.api.model.CompanyAdminPagedResponse
import cz.deuss.companyservice.api.model.CompanyCreateRequest
import cz.deuss.companyservice.api.model.CompanyDetailResponse
import cz.deuss.companyservice.api.model.CompanyPagedResponse
import cz.deuss.companyservice.api.model.CompanyStatusUpdateRequest
import cz.deuss.companyservice.api.model.CompanyUpdateRequest
import cz.deuss.companyservice.api.model.CompanyUserPagedResponse
import cz.deuss.companyservice.api.model.CompanyVerificationStatus
import cz.deuss.companyservice.api.model.CountryCode
import cz.deuss.companyservice.api.model.GetCompaniesSortByParameter
import cz.deuss.companyservice.api.model.OrderDirection
import cz.deuss.companyservice.api.model.ValidatorResponse
import cz.deuss.companyservice.api.model.WalletCreateRequest
import cz.deuss.companyservice.api.model.WalletCreateResponse
import cz.deuss.companyservice.database.model.Company
import cz.deuss.companyservice.mappers.validatorResponseList
import cz.deuss.companyservice.service.CompanyService
import cz.deuss.companyservice.service.EbsiValidationService
import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.authentication.AuthenticationService
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.http.HttpResponse
import io.micronaut.openapi.visitor.security.SecurityRule
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import java.util.UUID

@ExposedController
@ExecuteOn(TaskExecutors.IO)
@Secured(SecurityRule.IS_AUTHENTICATED)
open class CompaniesController(
    private val companyService: CompanyService,
    private val ebsiValidationService: EbsiValidationService,
    private val authenticationService: AuthenticationService,
    private val authUserId: () -> AuthenticatedUserId,
) : CompaniesApi {

    private val logger = KotlinLogging.logger {}

    private val semanticLogger = DeussLogger.semanticLogger(this::class)

    override fun completeCompanyRegistration(companyId: UUID): HttpResponse<Void> {
        TODO("Not yet implemented")
    }

    @Secured(Role.ADMIN)
    override fun getCompanies(
        page: Int,
        pageSize: Int,
        name: String?,
        registrationEntity: CountryCode?,
        verificationState: CompanyVerificationStatus?,
        originatorName: String?,
        walletAddress: String?,
        sortBy: GetCompaniesSortByParameter?,
        sortDirection: OrderDirection?
    ): CompanyAdminPagedResponse {
        logger.info { "Getting companies page#$page pageSize#$pageSize" }

        return companyService.getCompanies(
            page,
            pageSize,
            name,
            registrationEntity,
            verificationState,
            originatorName,
            walletAddress,
            sortBy,
            sortDirection,
        ).also {
            logger.info { "Companies Result#{page=${it.page}, count=${it.count}}" }
        }
    }

    override fun getCompanyById(companyId: UUID): CompanyDetailResponse {
        logger.info { "Getting company by Company#$companyId" }
        return companyService.getCompanyDetailById(companyId, authenticationService.extractAuthentication()).also {
            logger.info { "Returning Company#${it.companyId}" }
        }
    }

    // todo check that user is admin or employee of requested originator
    @Secured(value = [Role.ADMIN, Role.ORIGINATOR])
    override fun getOriginatorManagedCompanies(
        originatorId: UUID,
        page: Int,
        pageSize: Int
    ): CompanyPagedResponse {
        return companyService.getOriginatorManagedCompanies(originatorId, authUserId(), page, pageSize)
    }

    override fun createNewCompany(companyCreateRequest: CompanyCreateRequest): HttpResponse<CompanyDetailResponse> {
        val auth = authenticationService.extractAuthentication()
        return HttpResponse.created(
            companyService.insertNewCompany(
                companyCreateRequest,
                auth,
            )
        )
    }

    override fun myCompanies(
        page: Int,
        pageSize: Int,
    ): CompanyUserPagedResponse {
        val userId = authUserId()
        logger
            .info { "Getting User#${userId.id}'s companies with page: $page, Page: $pageSize" }
        return companyService.getMyCompanies(userId, page, pageSize).also {
            logger.info { "Returning User#$userId's Result#{page=${it.page}, count=${it.count}}" }
        }
    }

    override fun patchCompany(
        companyId: UUID,
        companyUpdateRequest: CompanyUpdateRequest,
    ): CompanyDetailResponse = companyService.updateCompany(
        companyId = companyId,
        companyUpdate = companyUpdateRequest,
        auth = authenticationService.extractAuthentication(),
    )

    @Secured(Role.ADMIN)
    override fun updateCompaniesStatus(
        companyId: UUID,
        companyStatusUpdateRequest: CompanyStatusUpdateRequest
    ): CompanyDetailResponse {
        val verificationState = Company.VerificationState.valueOf(companyStatusUpdateRequest.verificationState.name)
        return companyService.updateCompanyStatus(companyId, verificationState)
    }

    override fun validate(companyId: UUID) {
        semanticLogger.action("validateCompany").started()
            .param("userId", authUserId().id)
            .param("companyId", companyId).info()

        ebsiValidationService.companyToEasyChange(companyId, authenticationService.extractAuthentication())
    }

    override fun createCompanyWallet(companyId: UUID, walletCreateRequest: WalletCreateRequest): HttpResponse<WalletCreateResponse> {
        // todo check that user is admin or employee of company
        logger.info { "Creating wallet for Company#$companyId" }
        val walletAddress = companyService.putCompanyOnChain(companyId, walletCreateRequest.ownerId)
        return HttpResponse.created(WalletCreateResponse(walletAddress)).also {
            logger.info { "Created wallet for Company#$companyId with address#$walletAddress" }
        }

    }

    override fun getValidatorList(): List<ValidatorResponse> {
        return validatorResponseList(ebsiValidationService.getValidators())
    }

    override fun deleteCompany(companyId: UUID): HttpResponse<Void> {
        // todo check that user is employee, originator or admin
        companyService.deleteCompany(companyId)
        return HttpResponse.noContent()
    }
}
