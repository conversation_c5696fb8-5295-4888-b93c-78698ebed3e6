package cz.deuss.companyservice.mappers

import cz.deuss.companyservice.api.model.CompanyAdminPagedResponse
import cz.deuss.companyservice.api.model.CompanyAdminSummary
import cz.deuss.companyservice.api.model.CompanyCreateRequest
import cz.deuss.companyservice.api.model.CompanyDetailResponse
import cz.deuss.companyservice.api.model.CompanyEmployeeSummaryResponse
import cz.deuss.companyservice.api.model.CompanyEssentialsResponse
import cz.deuss.companyservice.api.model.CompanyPagedResponse
import cz.deuss.companyservice.api.model.CompanySummary
import cz.deuss.companyservice.api.model.CompanyType
import cz.deuss.companyservice.api.model.CompanyUpdateRequest
import cz.deuss.companyservice.api.model.CompanyUserPagedResponse
import cz.deuss.companyservice.api.model.CompanyUserSummary
import cz.deuss.companyservice.api.model.CompanyVerificationStatus
import cz.deuss.companyservice.api.model.EmployeeRole
import cz.deuss.companyservice.api.model.ShareholderDetailResponse
import cz.deuss.companyservice.api.model.ValidatorSummary
import cz.deuss.companyservice.database.model.Company
import cz.deuss.companyservice.database.model.CompanyTypeAssignment
import cz.deuss.companyservice.database.model.Employee
import cz.deuss.companyservice.database.model.Originator
import cz.deuss.companyservice.database.model.Shareholder
import cz.deuss.companyservice.database.model.Validator
import cz.deuss.companyservice.database.projection.CompanyEssentialsProjection
import cz.deuss.companyservice.service.CompanyEssentials
import cz.deuss.companyservice.service.CompanyUserDataDto
import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import io.micronaut.context.annotation.Mapper
import io.micronaut.context.annotation.Mapper.Mapping
import io.micronaut.core.convert.ConversionContext
import io.micronaut.core.convert.TypeConverter
import jakarta.inject.Singleton
import java.util.Optional

@Singleton
abstract class CompanyMapper(
    private val shareholderMapper: ShareholderMapper,
    private val employeeRoleMapper: EmployeeRoleMapper,
    private val addressMapper: AddressMapper,
) {
    fun toDetail(company: Company, employeeRole: Employee.Role, includeValidatorMessage: Boolean = false): CompanyDetailResponse {
        return CompanyDetailResponse(
            companyName = company.name,
            businessId = company.businessId,
            vatNumber = company.vatNumber,
            website = company.website,
            registrationDate = company.registrationDate,
            iban = company.iban,
            registrationEntity = company.registrationEntity.toDto(),
            addresses = company.addresses.map { address -> addressMapper.toResponse(address) },
            headcount = company.headcount.toDto(),
            annualTurnoverEur = company.annualTurnover.toDto(),
            balanceSheetTotalEur = company.balanceSheetTotal.toDto(),
            ownershipStatus = company.ownershipStatus.toDto(),
            contactEmail = company.contactEmail,
            contactPhone = company.contactPhone ?: "",
            created = company.created,
            lastEdit = company.lastEdit,
            companyId = company.id,
            verificationState = CompanyVerificationStatus.fromValue(company.verificationState.name),
            types = company.types.map { CompanyType.fromValue(it.type.name) },
            originatorId = company.originator?.id,
            validator = company.validator?.let { toSummary(it) },
            employeeRole = EmployeeRole.fromValue(employeeRole.name),
            legalEntityId = company.legalEntityId,
            walletAddress = company.walletAddress?.toString(),
        ).also {
            if (includeValidatorMessage) { it.lastValidatorMessage = company.stateUpdates.lastOrNull()?.comment }
        }
    }

    @Mapper(value = [Mapping(to = "validatorId", from = "id"), Mapping(to = "validatorName", from = "name")])
    abstract fun toValidatorSummaryMapper(validator: Validator): ValidatorSummary

    fun toSummary(validator: Validator): ValidatorSummary {
        return ValidatorSummary(
            validatorId = validator.id,
            validatorName = validator.name,
        )
    }

    fun toValidator(summary: ValidatorSummary): Validator {
        return Validator(
            id = summary.validatorId,
            name = summary.validatorName,
        )
    }

    fun toSummary(company: Company): CompanySummary {
        return CompanySummary(
            companyId = company.id,
            companyName = company.name,
            verificationState = CompanyVerificationStatus.fromValue(company.verificationState.name),
            companyType = CompanyType.ENTERPRISE,
            walletAddress = company.walletAddress?.toString(),
        )
    }

    fun toCompanyUserSummary(company: Company, role: Employee.Role): CompanyUserSummary {
        return CompanyUserSummary(
            companyId = company.id,
            companyName = company.name,
            verificationState = CompanyVerificationStatus.fromValue(company.verificationState.name),
            companyType = CompanyType.ENTERPRISE,
            employeeRole = EmployeeRole.fromValue(role.name),
            walletAddress = company.walletAddress?.toString(),
        )
    }

    fun toCompanyAdminSummary(company: Company): CompanyAdminSummary {
        return CompanyAdminSummary(
            companyId = company.id,
            companyName = company.name,
            verificationState = CompanyVerificationStatus.fromValue(company.verificationState.name),
            registrationEntity = company.registrationEntity.toDto(),
            originatorId = company.originator?.id,
            originatorName = company.originator?.name
        )
    }

    fun toCompany(
        request: CompanyCreateRequest,
        originator: Originator? = null,
        userId: AuthenticatedUserId,
        validator: Validator?,
        verificationState: Company.VerificationState = Company.VerificationState.DRAFT,
    ): Company {
        return Company(
            name = request.companyName,
            businessId = request.businessId,
            vatNumber = request.vatNumber,
            website = request.website,
            registrationDate = request.registrationDate,
            iban = request.iban,
            registrationEntity = request.registrationEntity.toDomain(),
            headcount = request.headcount.toDomain(),
            annualTurnover = request.annualTurnoverEur.toDomain(),
            balanceSheetTotal = request.balanceSheetTotalEur.toDomain(),
            ownershipStatus = request.ownershipStatus.toDomain(),
            contactEmail = request.contactEmail,
            contactPhone = request.contactPhone?.ifEmpty { null },
            verificationState = verificationState,
            legalEntityId = request.legalEntityId,
            authorId = userId.id,
        ).also {
            if (originator != null) { it.originator = originator }
            if (validator != null) { it.validator = validator }
        }
    }

    fun mapShareholders(shareholders: Collection<Shareholder>): List<ShareholderDetailResponse> =
        shareholders.map { shareholderMapper.toDetail(it) }

    fun toPagedResponse(companies: Collection<Company>, page: Int, totalCount: Long): CompanyPagedResponse =
        CompanyPagedResponse(
            companies = companies.map { toSummary(it) },
            count = totalCount,
            page = page
        )

    fun toUserPagedResponse(employees: List<Employee>, page: Int, totalCount: Long): CompanyUserPagedResponse =
        CompanyUserPagedResponse(
            companies = employees.map { employee ->
                toCompanyUserSummary(employee.company, employee.role)
            },
            count = totalCount,
            page = page
        )

    fun toAdminPagedResponse(companies: Collection<Company>, page: Int, totalCount: Long): CompanyAdminPagedResponse =
        CompanyAdminPagedResponse(
            companies = companies.map { toCompanyAdminSummary(it) },
            count = totalCount,
            page = page
        )

    fun updateEntity(entity: Company, update: CompanyUpdateRequest): Company {
        update.companyName?.let { entity.name = it }
        update.vatNumber?.let { entity.vatNumber = it }
        update.iban?.let { entity.iban = it }
        update.website?.let { entity.website = it }
        update.businessId?.let { entity.businessId = it }
        update.registrationDate?.let { entity.registrationDate = it }
        update.contactPhone?.let { entity.contactPhone = it }
        update.contactEmail?.let { entity.contactEmail = it }
        update.legalEntityId?.let { entity.legalEntityId = it }
        return entity
    }

    @Mapping
    abstract fun companyEssentialsProjectionToDto(companyEssentialsProjection: CompanyEssentialsProjection): CompanyEssentials

    fun companyEmployeeSummaryDtoToResponse(companyUserDataDto: CompanyUserDataDto): CompanyEmployeeSummaryResponse {
        val employeeRole = employeeRoleMapper.toDto(companyUserDataDto.employeeRole)
        return CompanyEmployeeSummaryResponse(
            typeDiscriminator = null,
            companyEssentialsDtoToResponse(companyUserDataDto.company),
            employeeRole
        )
    }

    @Mapping
    abstract fun companyEssentialsDtoToResponse(companyEssentials: CompanyEssentials): CompanyEssentialsResponse

}


@Singleton
abstract class CompanyTypeMapper {

    @Mapping(to = "company", from = "#{company}")
    @Mapping(to = "type", from = "#{companyType}")
    abstract fun toEntity(companyType: CompanyType, company: Company): CompanyTypeAssignment

    fun toEntities(types: Collection<CompanyType>, company: Company): Set<CompanyTypeAssignment> =
        types.mapTo(mutableSetOf()) { toEntity(it, company) }

    @Mapping(from = "type", to = "value")
    abstract fun toCompanyType(assignment: CompanyTypeAssignment): CompanyType

}


@Singleton
class CompanyTypeAssignmentConverter(
    private val companyTypeMapper: CompanyTypeMapper,
) : TypeConverter<CompanyTypeAssignment, CompanyType> {
    override fun convert(
        `object`: CompanyTypeAssignment,
        targetType: Class<CompanyType>,
        context: ConversionContext,
    ): Optional<CompanyType> = Optional.of(companyTypeMapper.toCompanyType(`object`))
}
