package cz.deuss.companyservice.service

import com.fasterxml.jackson.annotation.JsonProperty
import cz.deuss.companyservice.api.model.CountryCode
import cz.deuss.companyservice.mappers.EasyChangeCompany
import io.micronaut.context.annotation.Requires
import io.micronaut.core.async.annotation.SingleResult
import io.micronaut.http.HttpHeaders.AUTHORIZATION
import io.micronaut.http.HttpHeaders.USER_AGENT
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Consumes
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.Headers
import io.micronaut.http.annotation.Part
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Produces
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.multipart.MultipartBody
import io.micronaut.http.multipart.CompletedFileUpload
import io.micronaut.http.multipart.FileUpload
import io.micronaut.serde.annotation.Serdeable
import java.io.File
import java.util.UUID

@Client("\${validation.host}")
@Headers(
    Header(name = USER_AGENT, value = "DEUSS blockchain"),
    Header(name = AUTHORIZATION, value = "Bearer \${validation.bearer-token}"),
)
@Requires(property = "validation.mock-enabled", value = "false", defaultValue = "false")
interface EbsiValidationClient {

    @Post("/smi")
    @SingleResult
    fun postCompany(@Body request: EasyChangeCompany): EbsiValidationResponse

    @Post("/documents/smi/{smeUid}/documents")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.MULTIPART_FORM_DATA)
    @SingleResult
    fun uploadDocument(
        @PathVariable smeUid: UUID,
        @Body requestBody: MultipartBody
    ): UploadDocumentResponse

    @Get("/partner/validator/list")
    @SingleResult
    fun getValidatorList(): List<EbsiValidator>
}

@Serdeable.Deserializable
data class EbsiValidationResponse(
    val success: Boolean,
    val payload: Map<String, String>,
){
    fun getUid(): UUID? {
        return payload["uid"]?.let { UUID.fromString(it) }
    }
}

@Serdeable.Deserializable
data class EbsiValidator(
    val uid: UUID,
    val name: String,
    @JsonProperty("identification_number")
    val identificationNumber: String,
    @JsonProperty("country_code")
    val countryCode: CountryCode,
)

@Serdeable.Deserializable
data class UploadDocumentResponse(
    val success: Boolean,
    val payload: Map<String, String>,
)

enum class DocumentFileType {
    FINANCIAL_STATEMENT,
    COMPANY_STATUES,
    COMPANY_STRUCTURE,
    OTHER
}