package cz.deuss.companyservice.service

import cz.deuss.companyservice.api.model.CountryCode
import cz.deuss.companyservice.mappers.EasyChangeCompany
import io.micronaut.context.annotation.Requires
import io.micronaut.http.client.multipart.MultipartBody
import jakarta.inject.Singleton
import java.util.UUID

@Singleton
@Requires(property = "validation.mock-enabled", value = "true")
class MockEbsiValidationClient : EbsiValidationClient {

    override fun postCompany(request: EasyChangeCompany): EbsiValidationResponse {
        return EbsiValidationResponse(
            success = true,
            payload = mapOf("uid" to UUID.randomUUID().toString())
        )
    }

    override fun uploadDocument(smeUid: UUID, requestBody: MultipartBody): UploadDocumentResponse {
        return UploadDocumentResponse(
            success = true,
            payload = mapOf("documentId" to UUID.randomUUID().toString())
        )
    }

    override fun getValidatorList(): List<EbsiValidator> {
        return listOf(
            EbsiValidator(
                uid = UUID.fromString("550e8400-e29b-41d4-a716-************"),
                name = "Demo Validator CZ",
                identificationNumber = "12345678",
                countryCode = CountryCode.CZ
            ),
            EbsiValidator(
                uid = UUID.fromString("550e8400-e29b-41d4-a716-************"),
                name = "Demo Validator SK",
                identificationNumber = "87654321",
                countryCode = CountryCode.SK
            ),
            EbsiValidator(
                uid = UUID.fromString("550e8400-e29b-41d4-a716-************"),
                name = "Demo Validator DE",
                identificationNumber = "DE123456789",
                countryCode = CountryCode.DE
            )
        )
    }
}