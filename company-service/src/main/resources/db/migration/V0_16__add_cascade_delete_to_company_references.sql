ALTER TABLE employee DROP CONSTRAINT IF <PERSON>XISTS employee_company_fk;
ALTER TABLE shareholder DROP CONSTRAINT IF EXISTS shareholder_company_fk;
ALTER TABLE company_address DROP CONSTRAINT IF EXISTS company_address_company_fk;
ALTER TABLE company_document DROP CONSTRAINT IF EXISTS company_document_company_fk;
ALTER TABLE company_type_assignment DROP CONSTRAINT IF EXISTS company_type_company_fk;
ALTER TABLE company_state_update DROP CONSTRAINT IF EXISTS company_fk;

ALTER TABLE employee ADD CONSTRAINT employee_company_fk
    FOREIGN KEY (company_id) REFERENCES company (id) ON DELETE CASCADE;

ALTER TABLE shareholder ADD CONSTRAINT shareholder_company_fk
    FOREIGN KEY (company_id) REFERENCES company (id) ON DELETE CASCADE;

ALTER TABLE company_address ADD CONSTRAINT company_address_company_fk
    FOREIGN KEY (company_id) REFERENCES company (id) ON DELETE CASCADE;

ALTER TABLE company_document ADD CONSTRAINT company_document_company_fk
    FOREIGN KEY (company_id) REFERENCES company (id) ON DELETE CASCADE;

ALTER TABLE company_type_assignment ADD CONSTRAINT company_type_company_fk
    FOREIGN KEY (company_id) REFERENCES company (id) ON DELETE CASCADE;

ALTER TABLE company_state_update ADD CONSTRAINT company_fk
    FOREIGN KEY (company_id) REFERENCES company (id) ON DELETE CASCADE;