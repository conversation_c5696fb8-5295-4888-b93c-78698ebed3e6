micronaut:
  application:
    name: company-service
  server:
    port: 8080
    netty:
      access-logger:
        enabled: true
  security:
    token:
      propagation:
        header:
          enabled: true
          header-name: "Authorization"
        enabled: true
        service-id-regex: "file-service"
  http:
    services:
      file-service:
        urls:
          - ${FILE_SERVICE_URL}
        service-name: "company-service"
      user-service:
        attempts: ${USER_SERVICE_ATTEMPTS:`3`}
        delay: ${USER_SERVICE_ATTEMPTS_DELAY:`1s`}
        reset: ${USER_SERVICE_CIRCUIT_BREAKER_RESET:30s}
        urls:
          # DO NOT add default value. http: double dot is resolved as part of default value dropping http.
          - ${USER_SERVICE_URL}
        service-name: "company-service"

# Default argon2 config. Whenever changed, stored passwords need to be regenerated.
argon2-configuration:
  iterations: 10
  memory: 65536
  parallelism: 1

validators:
  credentials:
    username: ${VALIDATOR_USERNAME}
    password-hash: ${VALIDATOR_PASSWORD_HASH} # Argon2 hash of password. We don't store the original.

jpa:
  default:
    entity-scan:
      packages: 'cz.deuss.companyservice.database.model'
flyway:
  datasources:
    default:
      enabled: true
      locations: db/migration
datasources:
  default:
    db-type: postgres
    dialect: POSTGRES
    driver-class-name: org.postgresql.Driver
    url: ${DATABASE_URL:`***************************************************`}
    username: ${DATABASE_USER:company_service_user}
    password: ${DATABASE_PASSWORD:company_service}

deuss:
  blockchain:
    url: ${CHAIN_URL}
    contracts:
      company-wallet-registry: ${CHAIN_COMPANY_WALLET_REGISTRY_ADDRESS}
      bond-registry-v2: ${CHAIN_BOND_REGISTRY_V2_ADDRESS}
      admin-privateKey: ${CHAIN_ADMIN_PRIVATE_KEY}


validation:
  # get here https://vaultwarden.deussblockchain.eu/#/vault?organizationId=8538c467-0705-4dfc-8572-a68e24b1bb12&itemId=49c70255-a774-4cc3-97d0-c185a4b6f9b2
  bearer-token: ${VALIDATION_TOKEN}
  host: ${VALIDATION_HOST:`https://ebsi-dev.bonesystems.cz/api/`}

public-api:
  document:
    presigned-url:
      get:
        duration: PT5M
      put:
        duration: PT5M
