plugins {
    kotlin("jvm")
    alias(libs.plugins.ethers.abigen)
    alias(libs.plugins.kotlin.noarg)
    alias(libs.plugins.kotlin.allOpen)
    id("io.micronaut.library")
    alias(libs.plugins.google.ksp)

}

group = "cz.deuss"
version = "0.0.0"


dependencies {
    implementation("io.micronaut:micronaut-inject")
    implementation(libs.kotlin.logging)
    implementation(project(":deuss-platform-offchain-framework:framework"))
    implementation(libs.bundles.ethers)
}

micronaut {
    version = libs.versions.micronaut.version.get()
    processing {
        incremental(true)
        annotations("cz.deuss.companyservice.blockchain.provider**")
    }
}


ethersAbigen {
    directorySource("src/main/resources/abi")
    outputDir = "ethers/generated/source"
}
