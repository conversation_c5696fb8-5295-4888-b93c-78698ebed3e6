package cz.deuss.companyservice.blockchain.client

import cz.deuss.companyservice.blockchain.config.BlockchainConfiguration
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import deuss.contract.company.BondRegistryV2
import io.ethers.core.types.Address
import io.ethers.core.types.BlockId
import io.ethers.providers.middleware.Middleware
import io.ethers.signers.PrivateKeySigner
import jakarta.inject.Singleton
import java.math.BigInteger

/**
 * Implementation of BondRegistryClient that interacts with the BondRegistryV2 smart contract
 * to manage roles and other bond registry operations on the blockchain.
 */
@Singleton
class BondRegistryClientImpl(
    private val middleware: Middleware,
    private val blockchainConfiguration: BlockchainConfiguration,
) : BondRegistryClient {

    private val logger = DeussLogger.semanticLogger(BondRegistryClientImpl::class)

    private val adminSigner: PrivateKeySigner by lazy {
        PrivateKeySigner(blockchainConfiguration.adminPrivateKey)
    }

    private val bondRegistryV2: BondRegistryV2 by lazy {
        BondRegistryV2(middleware, blockchainConfiguration.bondRegistryV2Address)
    }


    override fun grantRoleToWallet(walletAddress: Address, role: BondRegistryRole) {
        logger.message("Granting role ${role.name} to wallet: $walletAddress").info()

        try {
            // Get the role value from the contract
            val roleValue = getRoleValue(role)

            logger.message("Executing grantRoles transaction for wallet: $walletAddress with role: ${role.name} (value: $roleValue)").debug()

            // Execute the blockchain transaction to grant the role
            val functionCall = bondRegistryV2.grantRoles(walletAddress, roleValue)
            val pendingTransaction = functionCall.send(adminSigner).sendAwait().unwrapOrElse {
                throw BondRegistryRoleException("Failed to execute grantRoles transaction for wallet: $walletAddress with role: ${role.name} (value: $roleValue)")
            }

            // Wait for the transaction to be mined
            pendingTransaction.awaitInclusion().unwrapOrElse {
                throw BondRegistryRoleException("Failed to wait for transaction to be mined for wallet: $walletAddress with role: ${role.name} (value: $roleValue)")
            }

            logger.message("Successfully granted role ${role.name} to wallet: $walletAddress")
                .info()


        } catch (e: Exception) {
            // This now catches other exceptions like network issues, etc.
            val genericErrorMessage = "A non-contract error occurred while granting role ${role.name} to wallet $walletAddress"
            logger.message(genericErrorMessage).throwable(e).error()
            throw BondRegistryRoleException(genericErrorMessage, e)
        }
    }

    /**
     * Private helper method to get the BigInteger value for a role from the contract.
     */
    private fun getRoleValue(role: BondRegistryRole): BigInteger {
        return try {
            val roleValue = when (role) {
                BondRegistryRole.PROPOSER -> bondRegistryV2.PROPOSER_ROLE().call(BlockId.LATEST).sendAwait().unwrap()
                BondRegistryRole.REGISTRAR -> bondRegistryV2.REGISTRAR_ROLE().call(BlockId.LATEST).sendAwait().unwrap()
                BondRegistryRole.CLOSER -> bondRegistryV2.CLOSER_ROLE().call(BlockId.LATEST).sendAwait().unwrap()
            }

            roleValue
        } catch (e: Exception) {
            logger.message("Failed to get role value for $role from contract")
                .throwable(e)
                .error()
            throw BondRegistryRoleException("Failed to get role value for $role from contract", e)
        }
    }
}
