import io.micronaut.gradle.openapi.tasks.OpenApiClientGenerator
import io.micronaut.gradle.openapi.tasks.OpenApiServerGenerator
import java.util.concurrent.CopyOnWriteArrayList

private val javaVersion: String by project

version = "0.1"
group = "cz.deuss"

plugins {
    id(libs.plugins.kotlin.noarg.get().pluginId)
    id(libs.plugins.kotlin.allOpen.get().pluginId)
    id(libs.plugins.kotlin.jvm.get().pluginId)
    id(libs.plugins.google.ksp.get().pluginId)
    id(libs.plugins.gradle.shadow.get().pluginId)
    id(libs.plugins.micronaut.application.get().pluginId)
    id(libs.plugins.micronaut.openapi.get().pluginId)
}

dependencies {
    ksp("io.micronaut.data:micronaut-data-processor")
    ksp("io.micronaut:micronaut-http-validation")
    ksp("io.micronaut.serde:micronaut-serde-processor")
    ksp("io.micronaut.validation:micronaut-validation-processor")
    ksp("io.micronaut.openapi:micronaut-openapi")
    ksp("io.micronaut.security:micronaut-security-annotations")
    implementation(project(":shared:deuss-common"))
    implementation(platform(libs.micronaut.platform))
    implementation("io.micronaut.data:micronaut-data-hibernate-jpa")
    implementation("io.micronaut.data:micronaut-data-tx-hibernate")
    implementation("io.micronaut.sql:micronaut-jdbc-hikari")
    implementation("io.micronaut.beanvalidation:micronaut-hibernate-validator")
    implementation("io.micronaut.flyway:micronaut-flyway")
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime")
    implementation("io.micronaut.serde:micronaut-serde-jackson")
    implementation("io.micronaut:micronaut-management")
    implementation("io.micronaut:micronaut-http-client")
    implementation("de.mkammerer:argon2-jvm:2.11")
    implementation(libs.bundles.kotlin)
    implementation(libs.bundles.micronaut.security)
    implementation(libs.bundles.micronaut.validation)
    implementation(libs.bundles.logging)
    implementation(project(":deuss-platform-offchain-framework:framework"))
    implementation(libs.bundles.ethers)
    implementation(project(":company-service:blockchain"))
    compileOnly("io.micronaut:micronaut-http-client")
    runtimeOnly("ch.qos.logback:logback-classic")
    runtimeOnly("com.fasterxml.jackson.module:jackson-module-kotlin")
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")
    runtimeOnly("org.yaml:snakeyaml")
    testImplementation("io.micronaut.reactor:micronaut-reactor")
    testImplementation(libs.bundles.test.base)
    testImplementation(platform(libs.test.containers.bom))
    testImplementation(libs.bundles.test.containers)
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testImplementation(project(":deuss-platform-offchain-framework:test"))
    testImplementation(libs.io.mockk)
    testImplementation(libs.bundles.test.kotest)
    testImplementation(libs.mockwebserver)
}

application {
    mainClass = "cz.deuss.companyservice.ApplicationKt"
}

java {
    sourceCompatibility = JavaVersion.toVersion(javaVersion)
}


graalvmNative.toolchainDetection = false

// re-build specifications before codegen
tasks.withType<OpenApiServerGenerator>() {
    // Make sure Gradle knows to track this input and run tasks in order
    dependsOn(":api-specifications:build")
}
// re-build specifications before codegen
tasks.withType<OpenApiClientGenerator>() {
    // Make sure Gradle knows to track this input and run tasks in order
    dependsOn(":api-specifications:build")
}

micronaut {
    version = libs.versions.micronaut.version.get()
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("cz.deuss.companyservice.*")
    }
    openapi {
        val specsSource = project(":api-specifications").layout.buildDirectory

        server(specsSource.get().dir("company-service").file("full.yaml").asFile) {
            apiPackageName = "cz.deuss.companyservice.api"
            modelPackageName = "cz.deuss.companyservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
        }

        client("file-service", specsSource.get().dir("file-service").file("internal.yaml").asFile) {
            apiPackageName = "cz.deuss.fileservice.api"
            modelPackageName = "cz.deuss.fileservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
            // THIS IS CLIENT ID NOT URL. url is configured based on clientId. see properties micronaut.http.services.file-service.urls
            // before changing this value search config files and possibly client filters, which modify behavior.
            clientId = "file-service"
            sortParamsByRequiredFlag = true
        }

        client("user-service", specsSource.get().dir("user-service").file("internal.yaml").asFile) {
            apiPackageName = "cz.deuss.userservice.api"
            modelPackageName = "cz.deuss.userservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
            // THIS IS CLIENT ID NOT URL. url is configured based on clientId. see properties micronaut.http.services.file-service.urls
            // before changing this value search config files and possibly client filters, which modify behavior.
            clientId = "user-service"
            sortParamsByRequiredFlag = true
        }
    }
}


tasks.named<io.micronaut.gradle.docker.NativeImageDockerfile>("dockerfileNative") {
    jdkVersion = javaVersion
}

noArg {
    annotation("jakarta.persistence.Entity")
}
allOpen {
    annotations("jakarta.persistence.Entity", "jakarta.persistence.MappedSuperclass")
}

tasks {
    test {
        maxParallelForks = 1
        val failedTests = CopyOnWriteArrayList<String>()

        testLogging {
            showStandardStreams = true
            showStackTraces = true
            showCauses = true
            showExceptions = true
            events("passed", "skipped", "failed")
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        }
        useJUnitPlatform {
            includeEngines("junit-jupiter", "kotest")
        }
        reports {
            junitXml.outputLocation = file("${project.layout.buildDirectory.get()}/reports/tests/test/xml")
        }

        // Collect failed test names
        afterTest(KotlinClosure2<TestDescriptor, TestResult, Unit>({ desc, result ->
            if (result.resultType == TestResult.ResultType.FAILURE) {
                failedTests.add("${desc.className?.substringAfterLast('.')}.${desc.name}")
            }
        }))

        // Print them after the full suite runs
        afterSuite(KotlinClosure2<TestDescriptor, TestResult, Unit>({ desc, _ ->
            if (desc.parent == null && failedTests.isNotEmpty()) {
                println("\n=== FAILED TESTS ===")
                failedTests.forEach { println(it) }
            }
        }))
    }
}
