rootProject.name = "offchain-microservices"

val deussGitlabTokenValue: String? by settings

pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
    }
}

dependencyResolutionManagement {
    @Suppress("UnstableApiUsage")
    repositories {
        mavenCentral()
        configureDeussGitlabRepositories()
    }
    versionCatalogs {
        create("deuss") {
            from(files("gradle/libs.versions.toml"))
        }
    }
}

plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.10.0"
}

// Include all service modules
include("bond-issuance-service")
include("bond-issuance-service:domain")
include("bond-issuance-service:blockchain")
include("company-service")
include("company-service:blockchain")
include("file-service")
include("kyc-service")
include("user-service")
include("deuss-platform-offchain-framework")
include("deuss-platform-offchain-framework:framework")
include("deuss-platform-offchain-framework:test")
include("deuss-platform-offchain-framework:chain")
include("api-specifications")
include("payment-service")
include("payment-service:blockchain")
include("shared:deuss-common")

// Note: deuss-platform-offchain-framework is excluded as it maintains its own build system
private fun RepositoryHandler.configureDeussGitlabRepositories() {
    val (tokenName, tokenValue) = provideCredentials()
    this.apply {
        maven {
            name = "deuss-platform-offchain-framework"
            url = uri("https://gitlab.nesad.fit.vutbr.cz/api/v4/projects/195/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                name = tokenName
                value = tokenValue
            }
            authentication {
                create("header", HttpHeaderAuthentication::class)
            }
        }
    }
}

private fun provideCredentials(): Pair<String, String> {
    System.getenv("CI")?.run {
        logger.info("Running in CI environment")
        return "Job-Token" to System.getenv("CI_JOB_TOKEN")
    }
    val value = requireNotNull(deussGitlabTokenValue) {
        "Gitlab token named deussGitlabTokenValue is not set. Please set it in .gradle/gradle.properties"
    }
    logger.info("Running in local environment")
    return "Private-Token" to value
}
