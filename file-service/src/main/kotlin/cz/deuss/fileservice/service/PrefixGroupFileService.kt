package cz.deuss.fileservice.service

import cz.deuss.fileservice.api.model.FileMetadataEntry
import cz.deuss.fileservice.api.model.FilesInner
import cz.deuss.fileservice.api.model.GetFileMetadataResponse
import cz.deuss.fileservice.api.model.GetFilesListResponse
import cz.deuss.fileservice.config.DeussConfigurationProperties
import io.micronaut.http.HttpResponse
import jakarta.inject.Singleton

@Singleton
class PrefixGroupFileService(
    private val s3Service: S3Service,
    private val deussConfigurationProperties: DeussConfigurationProperties
) {

    fun deleteFileByKey(key: FileKey): HttpResponse<Void> {
        s3Service.deleteFile(key.compoundKey)
        return HttpResponse.noContent()
    }

    fun getFileMetadata(
        key: FileKey,
    ): GetFileMetadataResponse {
        return s3Service.getFileMetadata(key.compoundKey).let {
            GetFileMetadataResponse(
                key.compoundKey,
                it.contentLength(),
                it.contentType(),
                it.metadata().map { (k, v) -> FileMetadataEntry(k, v) },
            )
        }
    }

    fun getFilesList(fileGroupId: String, fileCount: Int, continuationToken: String?): GetFilesListResponse {
        val collection = s3Service.listFiles(fileGroupId, fileCount, continuationToken)

        return GetFilesListResponse(
            collection.objects.map { s3Object ->
                FilesInner(
                    s3Object.key(),
                    s3Object.size(),
                    s3Object.lastModified().atZone(deussConfigurationProperties.defaultZoneId).toLocalDateTime(),
                )
            },
            collection.continuationToken
        )
    }
}
