import io.micronaut.gradle.openapi.tasks.OpenApiClientGenerator
import io.micronaut.gradle.openapi.tasks.OpenApiServerGenerator

private val javaVersion: String by project

version = "0.1"
group = "cz.deuss"

plugins {
    id(libs.plugins.kotlin.noarg.get().pluginId)
    id(libs.plugins.kotlin.allOpen.get().pluginId)
    id(libs.plugins.kotlin.jvm.get().pluginId)
    id(libs.plugins.google.ksp.get().pluginId)
    id(libs.plugins.gradle.shadow.get().pluginId)
    id(libs.plugins.micronaut.application.get().pluginId)
    id(libs.plugins.micronaut.openapi.get().pluginId)
}

dependencies {
    ksp("io.micronaut.data:micronaut-data-processor")
    ksp("io.micronaut:micronaut-http-validation")
    ksp("io.micronaut.serde:micronaut-serde-processor")
    ksp("io.micronaut.validation:micronaut-validation-processor")
    ksp("io.micronaut.openapi:micronaut-openapi")
    ksp("io.micronaut.security:micronaut-security-annotations")
    implementation(project(":shared:deuss-common"))
    implementation(platform(libs.micronaut.platform))
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime")
    implementation("io.micronaut.serde:micronaut-serde-jackson")
    implementation("io.micronaut.reactor:micronaut-reactor")
    implementation("io.micronaut.reactor:micronaut-reactor-http-client")
    implementation(libs.bundles.kotlin)
    implementation(libs.bundles.micronaut.validation)
    implementation(libs.bundles.micronaut.data)
    implementation(libs.bundles.micronaut.security)
    implementation(libs.bundles.logging)
    implementation(project(":deuss-platform-offchain-framework:framework"))
    implementation("io.micronaut.objectstorage:micronaut-object-storage-aws")
    implementation("software.amazon.awssdk:s3")
    compileOnly("io.micronaut:micronaut-http-client")
    runtimeOnly("ch.qos.logback:logback-classic")
    runtimeOnly("com.fasterxml.jackson.module:jackson-module-kotlin")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.yaml:snakeyaml")
    testImplementation(platform(libs.test.containers.bom))
    testImplementation(libs.bundles.test.base)
    testImplementation(libs.bundles.test.containers)
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testImplementation(project(":deuss-platform-offchain-framework:test"))
    testImplementation(libs.bundles.test.kotest)
}

application {
    mainClass = "cz.deuss.fileservice.ApplicationKt"
}
java {
    sourceCompatibility = JavaVersion.toVersion(javaVersion)
}


graalvmNative.toolchainDetection = false

micronaut {
    version = libs.versions.micronaut.version.get()
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("cz.deuss.fileservice.*")
    }
    openapi {
        val specsSource = project(":api-specifications").layout.buildDirectory

        server(specsSource.get().dir("file-service").file("internal.yaml").asFile) {
            apiPackageName = "cz.deuss.fileservice.api"
            modelPackageName = "cz.deuss.fileservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
        }
    }
}


tasks.named<io.micronaut.gradle.docker.NativeImageDockerfile>("dockerfileNative") {
    jdkVersion = javaVersion
}

noArg {
    annotation("jakarta.persistence.Entity")
}
allOpen {
    annotations("jakarta.persistence.Entity", "jakarta.persistence.MappedSuperclass")
}


tasks {
    // re-build specifications before codegen
    withType<OpenApiServerGenerator>() {
        // Make sure Gradle knows to track this input and run tasks in order
        dependsOn(":api-specifications:build")
    }
    // re-build specifications before codegen
    withType<OpenApiClientGenerator>() {
        // Make sure Gradle knows to track this input and run tasks in order
        dependsOn(":api-specifications:build")
    }

    test {
        testLogging {
            showStandardStreams = true
            showStackTraces = true
            showCauses = true
            showExceptions = true
            events("passed", "skipped", "failed")
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        }
        useJUnitPlatform {
            includeEngines("junit-jupiter", "kotest")
        }
        reports {
            junitXml.outputLocation = file("${project.layout.buildDirectory.get()}/reports/tests/test/xml")
        }
    }
}
