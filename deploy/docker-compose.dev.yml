---
x-default-deploy-policy: &default-deploy-policy
  deploy:
    restart_policy:
      condition: on-failure
      delay: 10s
      max_attempts: 5
      window: 60s
    resources:
      limits:
        cpus: '1.0'
        memory: 256M
      reservations:
        memory: 128M

x-default-chain-configuration: &default-chain-configuration
  CHAIN_URL: "http://**************:8546"
  CHAIN_BOND_REGISTRY_V2_ADDRESS: "******************************************"
  CHAIN_COMPANY_WALLET_REGISTRY_ADDRESS: "******************************************"

services:

  ### SERVICES ###
  file-service:
    # "latest" image is bad practice but good enough for now
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/file-service:latest
    ports:
      - "8093:8080"
    environment:
      MICRONAUT_ENVIRONMENTS: dev
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      AWS_ACCESS_KEY_ID: "$AWS_ACCESS_KEY_ID"
      AWS_SECRET_ACCESS_KEY: "$AWS_SECRET_ACCESS_KEY"
      AWS_REGION: "eu-central-1" # need to be set for micronaut-aws-sdk
      AWS_S3_HOST: "https://s3.vibe.feld.cvut.cz"
      AWS_S3_BUCKET: "13393-ebsi-dev"
      AWS_S3_PRESIGN_PUBLIC_URL: "https://dev.deussblockchain.eu/api/file-storage/"
    <<: *default-deploy-policy
    networks:
      - ebsi-dev

  kyc-service:
    # "latest" image is bad practice but good enough for now
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/kyc-service:latest
    ports:
      - "8092:8080"
    environment:
      MICRONAUT_ENVIRONMENTS: dev
      DATABASE_URL: "*************************************************"
      DATABASE_USER: "kyc_service"
      DATABASE_PASSWORD: "$KYC_SVC_PGPASS"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
    <<: *default-deploy-policy

    networks:
      - ebsi-dev

  user-service:
    # "latest" image is bad practice but good enough for now
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/user-service:latest
    ports:
      - "8091:8080"
    environment:
      MICRONAUT_ENVIRONMENTS: dev
      DATABASE_URL: "***************************************************"
      DATABASE_USER: "user_service"
      DATABASE_PASSWORD: "$USER_SVC_PGPASS"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      SMTP_USERNAME: "$SMTP_USERNAME"
      SMTP_PASSWORD: "$SMTP_PASSWORD"
    <<: *default-deploy-policy
    deploy:
      resources:
        limits:
          memory: 512M
    networks:
      - ebsi-dev

  company-service:
    # "latest" image is bad practice but good enough for now
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/company-service:latest
    ports:
      - "8090:8080"
    environment:
      <<: *default-chain-configuration
      MICRONAUT_ENVIRONMENTS: dev
      DATABASE_URL: "*********************************************************"
      DATABASE_USER: "company_service"
      DATABASE_PASSWORD: "$COMPANY_SVC_PGPASS"
      VALIDATION_TOKEN: "$VALIDATION_TOKEN"
      VALIDATION_HOST: "$VALIDATION_HOST"
      VALIDATOR_USERNAME: "$VALIDATOR_USERNAME"
      VALIDATOR_PASSWORD_HASH: "$VALIDATOR_PASSWORD_HASH"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      FILE_SERVICE_URL: "http://file-service:8080"
      USER_SERVICE_URL: "http://user-service:8080"
      CHAIN_URL: "http://**************:8546"
      CHAIN_ADMIN_PRIVATE_KEY: "$DEV_CHAIN_COMPANY_SERVICE_PRIVATE_KEY"
    <<: *default-deploy-policy
    deploy:
      resources:
        limits:
          memory: 512M
    networks:
      - ebsi-dev

  bond-issuance-service:
    # "latest" image is bad practice but good enough for now
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/bond-issuance-service:latest
    ports:
      - "8094:8080"
    environment:
      <<: *default-chain-configuration
      MICRONAUT_ENVIRONMENTS: dev
      DATABASE_URL: "*********************************************************************"
      DATABASE_USER: "bond_issuance_service"
      DATABASE_PASSWORD: "$BOND_ISSUANCE_SVC_PGPASS"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      FILE_SERVICE_URL: "http://file-service:8080"
      COMPANY_SERVICE_URL: "http://company-service:8080"
      CHAIN_INDEXER_URL: "http://indexer-api:8350"
      CHAIN_BOND_ISSUANCE_REGISTRAR_PRIVATE_KEY: "$CHAIN_BOND_ISSUANCE_REGISTRAR_PRIVATE_KEY"
    <<: *default-deploy-policy
    deploy:
      resources:
        limits:
          memory: 512M
    networks:
      - ebsi-dev

  payment-service:
    # "latest" image is bad practice but good enough for now
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/payment-service:latest
    ports:
      - "8095:8080"
    environment:
      MICRONAUT_ENVIRONMENTS: dev
      DATABASE_URL: "*********************************************************"
      DATABASE_USER: "payment_service"
      DATABASE_PASSWORD: "$PAYMENT_SVC_PGPASS"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      INDEXER_URL: "http://indexer-api:8350"
      PAYMENT_PROVIDER_PASSWORD_HASH: "$PAYMENT_PROVIDER_PASSWORD_HASH"
      PAYMENT_PROVIDER_USERNAME: "$PAYMENT_PROVIDER_USERNAME"
      PAYMENT_PROVIDER_TOKEN: "$PAYMENT_PROVIDER_TOKEN"
      PAYMENT_PROVIDER_HOST: "$PAYMENT_PROVIDER_HOST"
    <<: *default-deploy-policy
    networks:
      - ebsi-dev

  ### DATABASES ###
  company-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "company_service"
      POSTGRES_USER: "company_service"
      POSTGRES_PASSWORD: "$COMPANY_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
    volumes:
      - "/opt/besu-dev/company-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "5433:5432"
    networks:
      - ebsi-dev

  kyc-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "kyc_service"
      POSTGRES_USER: "kyc_service"
      POSTGRES_PASSWORD: "$KYC_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
      SMTP_USERNAME: "$SMTP_USERNAME"
      SMTP_PASSWORD: "$SMTP_PASSWORD"
    volumes:
      - "/opt/besu-dev/kyc-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "5434:5432"
    networks:
      - ebsi-dev

  user-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "user_service"
      POSTGRES_USER: "user_service"
      POSTGRES_PASSWORD: "$USER_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
    volumes:
      - "/opt/besu-dev/user-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "5435:5432"
    networks:
      - ebsi-dev

  bond-issuance-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "bond_issuance_service"
      POSTGRES_USER: "bond_issuance_service"
      POSTGRES_PASSWORD: "$BOND_ISSUANCE_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
    volumes:
      - "/opt/besu-dev/bond-issuance-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "5436:5432"
    networks:
      - ebsi-dev

  payment-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "payment_service"
      POSTGRES_USER: "payment_service"
      POSTGRES_PASSWORD: "$PAYMENT_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
    volumes:
      - "/opt/besu-dev/payment-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "5437:5432"
    networks:
      - ebsi-dev

networks:
  ebsi-dev:
    external: true
