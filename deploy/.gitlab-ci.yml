---
.deploy:
  image: registry.gitlab.fel.cvut.cz/svti/images/alpine/ci:latest
  needs:
    - job: 'docker:bond-issuance-service'
      optional: true
    - job: 'docker:company-service'
      optional: true
    - job: 'docker:file-service'
      optional: true
    - job: 'docker:user-service'
      optional: true
    - job: 'docker:kyc-service'
      optional: true
  tags: ['cvut-fel']
  allow_failure: true
  when: manual

deploy:dev:
  extends: .deploy
  variables:
    DOCKER_PROJECT: offchain-dev
    DOCKER_COMPOSE_FILE: docker-compose.dev.yml
  before_script:
    - cd deploy
    - apk add dos2unix openssh-client-default
    - mkdir -p ~/.ssh && echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
    - eval $(ssh-agent -s)
    - echo -n "$SSH_PRIVATE_KEY" | dos2unix | ssh-add -
  script:
    - scp docker-compose.dev.yml '<EMAIL>:'
    - |
      ssh <EMAIL> -C "
        export COMPANY_SVC_PGPASS="$DEV_COMPANY_SVC_PGPASS" && \
        export KYC_SVC_PGPASS="$DEV_KYC_SVC_PGPASS" && \
        export USER_SVC_PGPASS="$DEV_USER_SVC_PGPASS" && \
        export BOND_ISSUANCE_SVC_PGPASS="$DEV_BOND_ISSUANCE_SVC_PGPASS" && \
        export PAYMENT_SVC_PGPASS="$DEV_PAYMENT_SVC_PGPASS" && \
        export JWT_GENERATOR_SIGNATURE_SECRET="$DEV_JWT_GENERATOR_SIGNATURE_SECRET" && \
        export VALIDATION_TOKEN="$DEV_VALIDATION_TOKEN" && \
        export VALIDATION_HOST="$DEV_VALIDATION_HOST" && \
        export VALIDATOR_USERNAME="$DEV_VALIDATOR_USERNAME" && \
        export VALIDATION_PASSWORD_HASH="$DEV_VALIDATOR_PASSWORD_HASH" && \
        export SMTP_USERNAME="$DEV_SMTP_USERNAME" && \
        export SMTP_PASSWORD="$DEV_SMTP_PASSWORD" && \
        export AWS_ACCESS_KEY_ID="$DEV_AWS_ACCESS_KEY_ID" && \
        export AWS_SECRET_ACCESS_KEY="$DEV_AWS_SECRET_ACCESS_KEY" && \
        export INDEXER_URL="$DEV_INDEXER_URL" && \
        export BLOCKCHAIN_URL="$DEV_BLOCKCHAIN_URL" && \
        export DEV_CHAIN_COMPANY_WALLET_REGISTRY_ADDRESS="$DEV_CHAIN_COMPANY_WALLET_REGISTRY_ADDRESS" && \
        export DEV_CHAIN_BOND_REGISTRY_V2_ADDRESS="$DEV_CHAIN_BOND_REGISTRY_V2_ADDRESS" && \
        export DEV_CHAIN_COMPANY_SERVICE_PRIVATE_KEY="$DEV_CHAIN_COMPANY_SERVICE_PRIVATE_KEY" && \
        export PAYMENT_PROVIDER_PASSWORD_HASH="$DEV_PAYMENT_PROVIDER_PASSWORD_HASH" && \
        export PAYMENT_PROVIDER_USERNAME="$DEV_PAYMENT_PROVIDER_USERNAME" && \
        export PAYMENT_PROVIDER_TOKEN="$DEV_PAYMENT_PROVIDER_TOKEN" && \
        export PAYMENT_PROVIDER_HOST="$DEV_PAYMENT_PROVIDER_HOST" && \
        export CHAIN_BOND_REGISTRY_V2_ADDRESS="$DEV_CHAIN_BOND_REGISTRY_V2_ADDRESS" && \
        export CHAIN_BOND_ISSUANCE_REGISTRAR_PRIVATE_KEY="$DEV_CHAIN_BOND_ISSUANCE_REGISTRAR_PRIVATE_KEY" && \
        docker compose -p ${DOCKER_PROJECT} -f ${DOCKER_COMPOSE_FILE} up --detach --force-recreate --no-build --pull always --remove-orphans --wait --wait-timeout 30"

deploy:test:
  extends: .deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
      when: manual
    - if: '$CI_COMMIT_TAG !~ /^$/'
      when: manual
    - when: never
  variables:
    DOCKER_PROJECT: offchain-test
    DOCKER_COMPOSE_FILE: docker-compose.test.yml
  before_script:
    - cd deploy
    - apk add dos2unix openssh-client-default
    - mkdir -p ~/.ssh && echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
    - eval $(ssh-agent -s)
    - echo -n "$SSH_PRIVATE_KEY" | dos2unix | ssh-add -
  script:
    - scp docker-compose.test.yml '<EMAIL>:'
    - |
      ssh <EMAIL> -C "
        export COMPANY_SVC_PGPASS="$TEST_COMPANY_SVC_PGPASS" && \
        export KYC_SVC_PGPASS="$TEST_KYC_SVC_PGPASS" && \
        export USER_SVC_PGPASS="$TEST_USER_SVC_PGPASS" && \
        export JWT_GENERATOR_SIGNATURE_SECRET="$TEST_JWT_GENERATOR_SIGNATURE_SECRET" && \
        export VALIDATION_TOKEN="$TEST_VALIDATION_TOKEN" && \
        export VALIDATION_HOST="$TEST_VALIDATION_HOST" && \
        export VALIDATOR_USERNAME="$TEST_VALIDATOR_USERNAME" && \
        export VALIDATION_PASSWORD_HASH="$TEST_VALIDATOR_PASSWORD_HASH" && \
        export SMTP_USERNAME="$TEST_SMTP_USERNAME" && \
        export SMTP_PASSWORD="$TEST_SMTP_PASSWORD" && \
        export AWS_ACCESS_KEY_ID="$TEST_AWS_ACCESS_KEY_ID" && \
        export AWS_SECRET_ACCESS_KEY="$TEST_AWS_SECRET_ACCESS_KEY" && \
        export INDEXER_URL="$TEST_INDEXER_URL" && \
        export PAYMENT_PROVIDER_PASSWORD_HASH="$TEST_PAYMENT_PROVIDER_PASSWORD_HASH" && \
        export PAYMENT_PROVIDER_USERNAME="$TEST_PAYMENT_PROVIDER_USERNAME" && \
        export PAYMENT_PROVIDER_TOKEN="$DEV_PAYMENT_PROVIDER_TOKEN" && \
        export PAYMENT_PROVIDER_HOST="$DEV_PAYMENT_PROVIDER_HOST" && \
        docker compose -p ${DOCKER_PROJECT} -f ${DOCKER_COMPOSE_FILE} up --detach --force-recreate --no-build --pull always --remove-orphans --wait --wait-timeout 30"

deploy:demo:
  extends: .deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
      when: manual
    - if: '$CI_COMMIT_TAG !~ /^$/'
      when: manual
    - when: never
  variables:
    DOCKER_PROJECT: offchain-demo
    DOCKER_COMPOSE_FILE: docker-compose.demo.yml
  before_script:
    - cd deploy
    - apk add dos2unix openssh-client-default
    - mkdir -p ~/.ssh && echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
    - eval $(ssh-agent -s)
    - echo -n "$PILOT_SSH_KEY" | dos2unix | ssh-add -
  script:
    - scp docker-compose.demo.yml '<EMAIL>:'
    - |
      ssh <EMAIL> -C "
        export COMPANY_SVC_PGPASS="$DEMO_COMPANY_SVC_PGPASS" && \
        export KYC_SVC_PGPASS="$DEMO_KYC_SVC_PGPASS" && \
        export USER_SVC_PGPASS="$DEMO_USER_SVC_PGPASS" && \
        export JWT_GENERATOR_SIGNATURE_SECRET="$DEMO_JWT_GENERATOR_SIGNATURE_SECRET" && \
        export VALIDATION_TOKEN="$DEMO_VALIDATION_TOKEN" && \
        export VALIDATION_HOST="$DEMO_VALIDATION_HOST" && \
        export SMTP_USERNAME="$DEMO_SMTP_USERNAME" && \
        export SMTP_PASSWORD="$DEMO_SMTP_PASSWORD" && \
        export AWS_ACCESS_KEY_ID="$DEMO_AWS_ACCESS_KEY_ID" && \
        export AWS_SECRET_ACCESS_KEY="$DEMO_AWS_SECRET_ACCESS_KEY" && \
        export INDEXER_URL="$DEMO_INDEXER_URL" && \
        export PAYMENT_PROVIDER_PASSWORD_HASH="$DEMO_PAYMENT_PROVIDER_PASSWORD_HASH" && \
        export PAYMENT_PROVIDER_USERNAME="$DEMO_PAYMENT_PROVIDER_USERNAME" && \
        export PAYMENT_PROVIDER_TOKEN="$PILOT_PAYMENT_PROVIDER_TOKEN" && \
        export PAYMENT_PROVIDER_HOST="$PILOT_PAYMENT_PROVIDER_HOST" && \
        docker compose -p ${DOCKER_PROJECT} -f ${DOCKER_COMPOSE_FILE} up --detach --force-recreate --no-build --pull always --remove-orphans --wait --wait-timeout 30"

deploy:pilot:
  extends: .deploy
  rules:
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
      when: manual
    - if: '$CI_COMMIT_TAG !~ /^$/'
      when: manual
    - when: never
  variables:
    DOCKER_PROJECT: offchain-pilot
    DOCKER_COMPOSE_FILE: docker-compose.pilot.yml
  before_script:
    - cd deploy
    - apk add dos2unix openssh-client-default
    - mkdir -p ~/.ssh && echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
    - eval $(ssh-agent -s)
    - echo -n "$PILOT_SSH_KEY" | dos2unix | ssh-add -
  script:
    - scp docker-compose.pilot.yml '<EMAIL>:'
    - |
      ssh <EMAIL> -C "
        export COMPANY_SVC_PGPASS="$PILOT_COMPANY_SVC_PGPASS" && \
        export KYC_SVC_PGPASS="$PILOT_KYC_SVC_PGPASS" && \
        export USER_SVC_PGPASS="$PILOT_USER_SVC_PGPASS" && \
        export JWT_GENERATOR_SIGNATURE_SECRET="$PILOT_JWT_GENERATOR_SIGNATURE_SECRET" && \
        export VALIDATION_TOKEN="$PILOT_VALIDATION_TOKEN" && \
        export VALIDATION_HOST="$PILOT_VALIDATION_HOST" && \
        export VALIDATOR_USERNAME="$PILOT_VALIDATOR_USERNAME" && \
        export VALIDATION_PASSWORD_HASH="$PILOT_VALIDATOR_PASSWORD_HASH" && \
        export SMTP_USERNAME="$PILOT_SMTP_USERNAME" && \
        export SMTP_PASSWORD="$PILOT_SMTP_PASSWORD" && \
        export AWS_ACCESS_KEY_ID="$PILOT_AWS_ACCESS_KEY_ID" && \
        export AWS_SECRET_ACCESS_KEY="$PILOT_AWS_SECRET_ACCESS_KEY" && \
        export INDEXER_URL="$PILOT_INDEXER_URL" && \
        export PAYMENT_PROVIDER_PASSWORD_HASH="$PILOT_PAYMENT_PROVIDER_PASSWORD_HASH" && \
        export PAYMENT_PROVIDER_USERNAME="$PILOT_PAYMENT_PROVIDER_USERNAME" && \
        export PAYMENT_PROVIDER_TOKEN="$PILOT_PAYMENT_PROVIDER_TOKEN" && \
        export PAYMENT_PROVIDER_HOST="$PILOT_PAYMENT_PROVIDER_HOST" && \
        export USER_DEUSS_PGPASS="$PILOT_USER_DEUSS_PGPASS" && \
        docker compose -p ${DOCKER_PROJECT} -f ${DOCKER_COMPOSE_FILE} up --detach --force-recreate --no-build --pull always --remove-orphans --wait --wait-timeout 30"
