---
x-default-deploy-policy: &default-deploy-policy
  deploy:
    restart_policy:
      condition: on-failure
      delay: 10s
      max_attempts: 5
      window: 60s
    resources:
      limits:
        cpus: '1.0'
        memory: 256M
      reservations:
        memory: 128M
  

services:

  ### SERVICES ###
  file-service:
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/file-service:0.2.0
    environment:
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      AWS_ACCESS_KEY_ID: "$AWS_ACCESS_KEY_ID"
      AWS_SECRET_ACCESS_KEY: "$AWS_SECRET_ACCESS_KEY"
      AWS_REGION: "eu-central-1" # need to be set for micronaut-aws-sdk
      AWS_S3_HOST: "https://s3.vibe.feld.cvut.cz"
      AWS_S3_BUCKET: "13393-ebsi-test"
      AWS_S3_PRESIGN_PUBLIC_URL: "https://test.deussblockchain.eu/api/file-storage/"
    <<: *default-deploy-policy
    networks:
      - ebsi-test

  company-service:
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/company-service:0.6.2
    environment:
      MICRONAUT_ENVIRONMENTS: test
      DATABASE_URL: "*********************************************************"
      DATABASE_USER: "company_service"
      DATABASE_PASSWORD: "$COMPANY_SVC_PGPASS"
      VALIDATION_TOKEN: "$VALIDATION_TOKEN"
      VALIDATION_HOST: "$VALIDATION_HOST"
      VALIDATOR_USERNAME: "$VALIDATOR_USERNAME"
      VALIDATOR_PASSWORD_HASH: "$VALIDATOR_PASSWORD_HASH"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      FILE_SERVICE_URL: "http://file-service:8080"
      USER_SERVICE_URL: "http://user-service:8080"
      CHAIN_URL: "http://**************:8546"
      CHAIN_COMPANY_WALLET_REGISTRY_ADDRESS: "$DEV_CHAIN_COMPANY_WALLET_REGISTRY_ADDRESS"
      CHAIN_BOND_REGISTRY_V2_ADDRESS: "$DEV_CHAIN_BOND_REGISTRY_V2_ADDRESS"
      CHAIN_ADMIN_PRIVATE_KEY: "$DEV_CHAIN_COMPANY_SERVICE_PRIVATE_KEY"
    <<: *default-deploy-policy
    deploy:
      resources:
        limits:
          memory: 512M
    networks:
      - ebsi-test

  kyc-service:
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/kyc-service:0.1.0
    environment:
      MICRONAUT_ENVIRONMENTS: test
      DATABASE_URL: "*************************************************"
      DATABASE_USER: "kyc_service"
      DATABASE_PASSWORD: "$KYC_SVC_PGPASS"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
    <<: *default-deploy-policy
    networks:
      - ebsi-test

  user-service:
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/user-service:0.4.0
    environment:
      MICRONAUT_ENVIRONMENTS: test
      DATABASE_URL: "***************************************************"
      DATABASE_USER: "user_service"
      DATABASE_PASSWORD: "$USER_SVC_PGPASS"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      SMTP_USERNAME: "$SMTP_USERNAME"
      SMTP_PASSWORD: "$SMTP_PASSWORD"
    <<: *default-deploy-policy
    deploy:
      resources:
        limits:
          memory: 512M
    networks:
      - ebsi-test

  payment-service:
    # "latest" image is bad practice but good enough for now
    image: registry.gitlab.nesad.fit.vutbr.cz:443/bebi/offchain-microservices-core/offchain/payment-service:latest
    environment:
      MICRONAUT_ENVIRONMENTS: test
      DATABASE_URL: "*********************************************************"
      DATABASE_USER: "payment_service"
      DATABASE_PASSWORD: "$PAYMENT_SVC_PGPASS"
      JWT_GENERATOR_SIGNATURE_SECRET: "$JWT_GENERATOR_SIGNATURE_SECRET"
      INDEXER_URL: "$INDEXER_URL"
      PAYMENT_PROVIDER_PASSWORD_HASH: "$PAYMENT_PROVIDER_PASSWORD_HASH"
      PAYMENT_PROVIDER_USERNAME: "$PAYMENT_PROVIDER_USERNAME"
      PAYMENT_PROVIDER_TOKEN: "$PAYMENT_PROVIDER_TOKEN"
      PAYMENT_PROVIDER_HOST: "$PAYMENT_PROVIDER_HOST"
    <<: *default-deploy-policy
    networks:
      - ebsi-test


  ### DATABASES ###
  company-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "company_service"
      POSTGRES_USER: "company_service"
      POSTGRES_PASSWORD: "$COMPANY_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
    volumes:
      - "/opt/besu-test/company-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "15433:5432"
    networks:
      - ebsi-test

  kyc-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "kyc_service"
      POSTGRES_USER: "kyc_service"
      POSTGRES_PASSWORD: "$KYC_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
    volumes:
      - "/opt/besu-test/kyc-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "15434:5432"
    networks:
      - ebsi-test

  user-service-pg:
    image: "postgres:17-alpine"
    environment:
      POSTGRES_DB: "user_service"
      POSTGRES_USER: "user_service"
      POSTGRES_PASSWORD: "$USER_SVC_PGPASS"
      PGDATA: "/var/lib/postgresql/data/pgdata"
    volumes:
      - "/opt/besu-test/user-service-pg:/var/lib/postgresql/data/pgdata"
    deploy:
      restart_policy:
        condition: any
      resources:
        limits:
          cpus: '2.0'
          memory: 256M
    ports:
      - "15435:5432"
    networks:
      - ebsi-test

networks:
  ebsi-test:
    external: true
